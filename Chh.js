// ==UserScript==
// @name         chh论坛
// @description  屏蔽用户和关键词
// <AUTHOR>
// @version      1.7
// @match        https://*.chiphell.com/*
// @grant        GM_getValue
// @grant        GM_setValue
// ==/UserScript==

(function () {
  "use strict";

  // 添加样式
  const style = document.createElement("style");
  style.textContent = `
.pls .avatar img {
  border-radius: 12px;
}
  
.chh-block-button {
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 1000;
  padding: 5px 10px;
  background-color: #000;
  box-shadow: 0 0 6px rgba(0, 0, 0, 0.2);
  color: #fff;
  border: none;
  border-radius: 6px;
  cursor: pointer;
}

.chh-dialog {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  padding: 20px 20px 40px 20px;
  border-radius: 10px;
  z-index: 1001;
  width: 550px;
  height: auto;
  overflow: auto;
  background-color: rgba(250, 250, 252, 0.5);
  border: 1px solid rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(30px) saturate(180%);
  box-shadow: -5px 1px 50px rgba(0, 0, 0, 0.2), inset 0px 0px 30px rgba(255, 255, 255, 0.9);
}

.chh-dialog-title {
  margin: -20px -20px 0px -20px;
  padding: 10px 20px;
  border-radius: 10px 10px 0 0;
  text-align: center;
  cursor: move;
  user-select: none;
  height: 15px;
}

.chh-close-button {
  position: absolute;
  top: 12px;
  right: 12px;
  width: 18px;
  height: 18px;
  background: black;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  transition: background 0.15s ease;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
  overflow: hidden;
  text-indent: -9999px;
  padding: 0;
  z-index: 1002;
}

.chh-close-button:hover {
  background: red;
}

.chh-close-line {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  width: 10px;
  height: 2px;
  background: white;
  transition: background 0.15s ease;
}

.chh-close-line-1 {
  transform: translate(-50%, -50%) rotate(45deg);
}

.chh-close-line-2 {
  transform: translate(-50%, -50%) rotate(-45deg);
}

.chh-user-list {
  background-color: rgb(131, 131, 145, 0.15);
  padding: 10px;
  border-radius: 8px;
  margin-bottom: 15px;
}

.chh-keyword-list {
  background-color: rgb(131, 131, 145, 0.15);
  padding: 10px;
  border-radius: 8px;
  margin: 15px 0 15px 0;
}

.chh-list-title {
  font-weight: 800;
  font-size: 14px;
  margin-bottom: 5px;
}

.chh-list-content {
  font-size: 12px;
}

.chh-list-footer {
  color: #666;
  margin-top: 5px;
  font-size: 11px;
}

.chh-input {
  border: 1px solid #dddddd;
  background-color: #fff;
  border-radius: 8px;
  font-size: 13px;
  padding: 5px 10px;
}

.chh-button {
  margin-left: 10px;
  border-radius: 8px;
  border: 0px solid black;
  background-color: #000;
  padding: 5px 10px;
  margin: 5px 15px;
  color: white;
  font-size: 13px;
  box-shadow: 0 0px 10px rgba(0, 0, 0, 0.2);
}

.chh-input-container {
  margin-bottom: 10px;
}

.pls .pi {
  padding-left: 0;
  text-align: center;
}


    `;
  document.head.appendChild(style);

  // 从暴力猴存储中读取屏蔽列表，如果没有则使用默认值
  var blockedUsernames = GM_getValue("blockedUsernames", []);
  var blockedUsernamesSet = new Set(blockedUsernames);
  var blockedKeywords = GM_getValue("blockedKeywords", []);

  // 保存屏蔽列表到暴力猴存储
  function saveBlockedLists() {
    GM_setValue("blockedUsernames", blockedUsernames);
    GM_setValue("blockedKeywords", blockedKeywords);
    blockedUsernamesSet = new Set(blockedUsernames);
  }

  // 添加帖子编号上标样式
  const postNumberStyle = document.createElement("style");
  postNumberStyle.textContent = `
        .postinfo em:first-child::after {
            content: attr(data-floor);
            font-size: 0.7em;
            vertical-align: super;
            margin-left: 1px;
        }
    `;
  document.head.appendChild(postNumberStyle);

  // 检查是否为一楼帖子
  function isFirstFloor(element) {
    if (!element) return false;

    // 查找父容器
    let container = element.id ? element : null;
    if (!container && element.closest) {
      container = element.closest('div[id^="post_"]') || element.closest('table[id^="pid"]');
    }

    if (!container) return false;

    // 快速检查：ID是否以_1结尾
    if (container.id && container.id.endsWith("_1")) {
      return true;
    }

    // 检查楼层标记
    let postNumber = container.querySelector(".postinfo em:first-child");
    if (postNumber) {
      let text = postNumber.textContent || "";
      if (text.match(/^1$|^1[^0-9]|楼主/)) {
        return true;
      }
    }

    // 检查作者标记
    if (container.querySelector(".authicn[title='楼主']")) {
      return true;
    }

    // 检查特定标记类
    if (container.classList.contains("firstfloor") || container.querySelector(".firstfloor")) {
      return true;
    }

    // 检查是否有楼主标记
    let authorInfo = container.querySelector(".authi");
    if (authorInfo?.textContent.includes("楼主")) {
      return true;
    }

    // 检查chiphell特有的一楼标记
    // 检查是否有"只看该作者"链接且URL中包含page=1
    let authorOnlyLink = container.querySelector('a[href*="forum.php?mod=viewthread"][href*="page=1"][href*="authorid"]');
    if (authorOnlyLink) {
      return true;
    }

    // 检查是否有电梯直达且显示1#的标记
    let floorMark = container.querySelector('a[href$="#"]');
    if (floorMark?.textContent.includes("1#")) {
      return true;
    }

    // 检查是否有编辑记录且是第一页
    if (container.querySelector(".editby, .editnote") && window.location.href.includes("-1-1.html")) {
      return true;
    }

    // 检查是否有目录标记（通常只在一楼出现）
    if (container.textContent.includes("目录") && container.textContent.includes("一、") && container.textContent.includes("二、")) {
      return true;
    }

    return false;
  }

  // 检查用户是否在屏蔽列表中
  function isUserBlocked(element) {
    if (!element) return false;

    try {
      // 获取用户名（优先从title属性获取，因为有些链接文本可能为空）
      let username = element.getAttribute("title") || element.textContent.trim();
      if (!username) {
        logVerbose("未找到用户名", {
          element: element.outerHTML,
          title: element.getAttribute("title"),
          text: element.textContent,
        });
        return false;
      }

      // 检查是否在屏蔽列表中
      const isBlocked = blockedUsernamesSet.has(username);
      log("检查用户名:", username, "是否屏蔽:", isBlocked);
      return isBlocked;
    } catch (e) {
      logError("检查用户是否被屏蔽时出错", e);
      return false;
    }
  }

  // 处理评分区域
  function processRatelogItem(userLink) {
    if (!userLink || userLink.hasAttribute("data-handled")) {
      logVerbose("评分链接已处理或无效:", userLink);
      return;
    }

    try {
      const username = userLink.getAttribute("title") || userLink.textContent.trim();
      logVerbose("开始处理评分项:", {
        username,
        href: userLink.href,
        classList: Array.from(userLink.classList),
        parentElement: userLink.parentElement?.tagName,
        path: getElementPath(userLink),
      });

      if (isUserBlocked(userLink)) {
        let ratelogItem = userLink.closest("dl");
        logVerbose("找到评分项容器:", {
          found: !!ratelogItem,
          container: ratelogItem?.tagName,
          classList: ratelogItem ? Array.from(ratelogItem.classList) : [],
          path: ratelogItem ? getElementPath(ratelogItem) : null,
        });

        if (ratelogItem) {
          logVerbose("隐藏评分项:", username);
          ratelogItem.style.display = "none";
          ratelogItem.setAttribute("data-blocked-ratelog", "true");
        }
      }
      userLink.setAttribute("data-handled", "true");
    } catch (e) {
      logError("处理评分项时出错", e, {
        userLink: userLink?.outerHTML,
        location: window.location.href,
      });
    }
  }

  // 处理页面上的所有评分区域
  function preProcessAllRatelogItems() {
    console.log("预处理所有评分区域");

    try {
      // 查找所有评分区域中的用户链接
      document.querySelectorAll(".ratelog a:not([data-handled])").forEach(processRatelogItem);
    } catch (e) {
      console.error("预处理评分区域时出错:", e);
    }
  }

  // 在脚本开始处添加配置
  const CONFIG = {
    selectors: {
      threadItem: [
        'tbody[id^="normalthread_"]', // 论坛列表帖子容器
        'tr[id^="normalthread_"]', // 论坛列表行
        ".threadlist_li", // 新版列表项
        ".thread", // 通用主题类
        ".pbw", // 搜索结果容器
        ".tl", // 搜索结果列表项
      ],
      threadTitle: [
        "a.xst", // 主题标题链接
        "a.s.xst", // 主题标题链接(带样式)
        ".common a", // 普通主题标题
        "th.common a", // 表格布局主题标题
        ".subject a", // 搜索结果标题
      ],
      authorLink: [
        'a.xw1[href*="space-uid-"]', // 加粗用户名
        'a.xi2[href*="space-uid-"]', // 用户信息弹出框
        '.authi a[href*="space-uid-"]', // 作者信息区链接
        '.by a[href*="space-uid-"]', // 列表页作者链接
        '.author a[href*="space-uid-"]', // 作者链接
        '.avtm[href*="space-uid-"]', // 头像链接
        'a[href^="space-uid-"]', // 通用用户链接
      ],
      postContent: {
        containers: [
          'div[id^="post_"]', // 帖子容器
          'table[id^="pid"]', // 帖子表格
          ".t_fsz", // 帖子内容区
          ".plc", // 帖子主体
          ".pls", // 作者信息侧栏
        ],
        authorInfo: [
          ".authi", // 作者信息区
          ".pls", // 作者信息侧栏
          ".avatar", // 头像区
          ".pi", // 帖子信息区
          ".user_avatar", // 用户头像
        ],
      },
    },
    debug: false, // 默认关闭调试模式
    debugVerbose: false, // 默认关闭详细日志
    debugOptions: {
      maxLogInterval: 1000, // 日志输出最小间隔（毫秒）
      batchSize: 10, // 批量处理的日志数量
      logBuffer: [], // 日志缓冲区
      lastLogTime: 0, // 上次输出日志的时间
    },
  };

  // 改进日志函数
  function log(...args) {
    if (!CONFIG.debug) return;

    const now = Date.now();
    if (now - CONFIG.debugOptions.lastLogTime > CONFIG.debugOptions.maxLogInterval) {
      console.log("[CHH屏蔽]", ...args);
      CONFIG.debugOptions.lastLogTime = now;
    }
  }

  // 改进详细日志函数
  function logVerbose(...args) {
    if (!CONFIG.debug || !CONFIG.debugVerbose) return;

    CONFIG.debugOptions.logBuffer.push(args);

    if (CONFIG.debugOptions.logBuffer.length >= CONFIG.debugOptions.batchSize) {
      const now = Date.now();
      if (now - CONFIG.debugOptions.lastLogTime > CONFIG.debugOptions.maxLogInterval) {
        // 批量输出日志
        CONFIG.debugOptions.logBuffer.forEach((logArgs) => {
          console.log("[CHH屏蔽]", ...logArgs);
        });
        CONFIG.debugOptions.logBuffer = [];
        CONFIG.debugOptions.lastLogTime = now;
      }
    }
  }

  // 改进错误日志函数
  function logError(context, error, ...args) {
    if (!CONFIG.debug) return;

    const now = Date.now();
    // 错误日志始终输出，不受间隔限制
    console.error("[CHH屏蔽][错误]", context, error?.message || error);
    if (error?.stack && CONFIG.debugVerbose) {
      console.error("[CHH屏蔽][堆栈]", error.stack);
    }
    if (args.length > 0 && CONFIG.debugVerbose) {
      console.error("[CHH屏蔽][详情]", ...args);
    }
  }

  // 添加调试控制函数
  function setDebugMode(enabled, verbose = false) {
    CONFIG.debug = enabled;
    CONFIG.debugVerbose = verbose;
    CONFIG.debugOptions.logBuffer = [];
    CONFIG.debugOptions.lastLogTime = 0;
    console.log("[CHH屏蔽] 调试模式:", enabled ? "开启" : "关闭", "详细日志:", verbose ? "开启" : "关闭");
  }

  // 添加性能监控
  function measurePerformance(name, fn) {
    if (!CONFIG.debug) return fn();

    const start = performance.now();
    const result = fn();
    const end = performance.now();
    const duration = end - start;

    if (duration > 100) {
      // 只记录执行时间超过100ms的操作
      log(`性能警告: ${name} 耗时 ${duration.toFixed(2)}ms`);
    }

    return result;
  }

  // 检查是否包含屏蔽的关键词
  function hasBlockedKeyword(text) {
    if (!text || !blockedKeywords?.length) return false;

    return blockedKeywords.some((keyword) => {
      if (!keyword?.trim()) return false;
      try {
        return text.toLowerCase().includes(keyword.toLowerCase());
      } catch (e) {
        log("关键词检查错误:", e, keyword);
        return false;
      }
    });
  }

  // 修改 handleThreadTitle 函数
  function handleThreadTitle(element) {
    try {
      if (!element || element.hasAttribute("data-title-checked")) return;

      // 标记为已检查
      element.setAttribute("data-title-checked", "true");

      // 获取标题文本
      const title = element.textContent.trim();
      if (!title) return;

      // 查找帖子容器
      let threadContainer = findThreadContainer(element);
      if (!threadContainer) {
        logVerbose("未找到帖子容器");
        return;
      }

      // 检查标题关键词
      const hasBlockedTitle = hasBlockedKeyword(title);

      // 检查作者 - 只检查主要作者链接
      let hasBlockedAuthor = false;
      // 首先尝试查找 by citxt 类下的作者链接
      let authorLinks = threadContainer.querySelectorAll('.by .citxt a[href*="space-uid-"]');
      if (!authorLinks || authorLinks.length === 0) {
        // 如果找不到，尝试查找 by 类下的作者链接
        authorLinks = threadContainer.querySelectorAll('.by a[href*="space-uid-"]');
      }
      if (!authorLinks || authorLinks.length === 0) {
        // 最后尝试查找 author 类下的作者链接
        authorLinks = threadContainer.querySelectorAll('.author a[href*="space-uid-"]');
      }

      // 只检查第一个作者链接（主要作者）
      if (authorLinks && authorLinks.length > 0) {
        if (isUserBlocked(authorLinks[0])) {
          hasBlockedAuthor = true;
          log("检测到被屏蔽作者:", authorLinks[0].textContent.trim());
        }
      }

      // 如果标题包含关键词或作者被屏蔽，则隐藏整个帖子
      if (hasBlockedTitle || hasBlockedAuthor) {
        log(`隐藏帖子: ${hasBlockedTitle ? "包含屏蔽关键词" : ""}${hasBlockedAuthor ? "作者被屏蔽" : ""}`);
        hideContainer(threadContainer);
      }
    } catch (e) {
      logError("处理帖子标题时出错", e);
    }
  }

  // 添加查找帖子容器的函数
  function findThreadContainer(element) {
    try {
      // 在论坛首页
      const threadItem = element.closest('tbody[id^="normalthread_"], tr[id^="normalthread_"], .threadlist_li, .thread');
      if (threadItem) {
        logVerbose("找到帖子列表项:", {
          container: threadItem.tagName,
          id: threadItem.id,
          classList: Array.from(threadItem.classList),
        });
        return threadItem;
      }

      // 在搜索结果页
      const searchItem = element.closest(".pbw, .tl");
      if (searchItem) {
        logVerbose("找到搜索结果项:", {
          container: searchItem.tagName,
          classList: Array.from(searchItem.classList),
        });
        return searchItem;
      }

      logVerbose("未找到帖子容器");
      return null;
    } catch (e) {
      logError("查找帖子容器时出错", e);
      return null;
    }
  }

  // 修改 checkAndHide 函数
  function checkAndHide(element) {
    try {
      if (!element || element.nodeType !== Node.ELEMENT_NODE) {
        return;
      }

      // 检查是否已处理
      if (element.hasAttribute("data-handled")) {
        return;
      }

      // 标记为已处理
      element.setAttribute("data-handled", "true");

      // 处理标题
      CONFIG.selectors.threadTitle.forEach((selector) => {
        try {
          const titles = element.matches(selector) ? [element] : Array.from(element.querySelectorAll(selector));

          titles.forEach((title) => {
            if (!title.hasAttribute("data-title-checked")) {
              handleThreadTitle(title);
            }
          });
        } catch (err) {
          log(`处理标题选择器时出错:`, err);
        }
      });

      // 处理所有可能的用户链接
      let processedLinks = new Set();
      CONFIG.selectors.authorLink.forEach((selector) => {
        try {
          const links = element.matches(selector) ? [element] : Array.from(element.querySelectorAll(selector));

          links.forEach((link) => {
            if (!link.hasAttribute("data-handled") && !processedLinks.has(link)) {
              processedLinks.add(link);
              handleUserLink(link);
            }
          });
        } catch (err) {
          log(`处理选择器时出错:`, err);
        }
      });

      if (processedLinks.size > 0) {
        logVerbose(`处理了 ${processedLinks.size} 个用户链接`);
      }

      // 处理子元素
      if (element.children && element.children.length > 0) {
        Array.from(element.children).forEach((child) => {
          if (!child.hasAttribute("data-handled")) {
            checkAndHide(child);
          }
        });
      }
    } catch (e) {
      log("处理元素时出错:", e);
    }
  }

  // 处理用户链接
  function handleUserLink(element) {
    try {
      if (!element) {
        logVerbose("handleUserLink: 元素为空");
        return;
      }

      element.setAttribute("data-handled", "true");
      const username = element.textContent.trim();

      // 详细记录元素信息
      logVerbose("处理用户链接:", {
        username,
        href: element.href,
        classList: Array.from(element.classList),
        parentElement: element.parentElement?.tagName,
        isRatelog: !!element.closest(".ratelog"),
      });

      // 检查是否在评分区域
      if (element.closest(".ratelog")) {
        logVerbose("用户链接在评分区域内，调用processRatelogItem");
        processRatelogItem(element);
        return;
      }

      if (isUserBlocked(element)) {
        let container = findNearestContainer(element);
        logVerbose("查找容器结果:", {
          username,
          foundContainer: !!container,
          containerType: container?.tagName,
          containerClasses: container ? Array.from(container.classList) : [],
        });

        if (container) {
          log(`屏蔽用户 "${username}" 的内容`);
          hideContainer(container);
        } else {
          logVerbose(`未找到用户 "${username}" 的内容容器`);
        }
      }
    } catch (e) {
      logError("处理用户链接时出错", e, {
        element: element?.outerHTML,
        location: window.location.href,
      });
    }
  }

  // 修改 findNearestContainer 函数
  function findNearestContainer(element) {
    if (!element) {
      logVerbose("findNearestContainer: 元素为空");
      return null;
    }

    try {
      // 检查是否在评分区域
      const isRatelog = element.closest(".ratelog, .rate");
      logVerbose("检查是否在评分区域:", {
        element: element.tagName,
        isRatelog: !!isRatelog,
        classList: Array.from(element.classList),
        path: getElementPath(element),
      });

      if (isRatelog) {
        logVerbose("元素在评分区域内，返回null");
        return null;
      }

      // 记录容器查找过程
      let containerInfo = {
        element: element.tagName,
        classList: Array.from(element.classList),
        path: getElementPath(element),
        checked: [],
      };

      // 首先尝试查找最近的帖子容器
      const postContainer = element.closest('div[id^="post_"], table[id^="pid"]');
      if (postContainer && !postContainer.closest(".ratelog, .rate")) {
        logVerbose("找到帖子容器:", {
          id: postContainer.id,
          type: postContainer.tagName,
          classList: Array.from(postContainer.classList),
        });
        return postContainer;
      }

      // 如果找不到帖子容器，再尝试其他容器
      for (let selector of CONFIG.selectors.postContent.containers) {
        let container = element.closest(selector);
        containerInfo.checked.push({
          selector,
          found: !!container,
          isRatelog: container ? !!container.closest(".ratelog, .rate") : false,
        });

        if (container && !container.closest(".ratelog, .rate")) {
          logVerbose("找到其他容器:", {
            selector,
            container: container.tagName,
            classList: Array.from(container.classList),
          });
          return container;
        }
      }

      logVerbose("容器查找结果:", containerInfo);
      return null;
    } catch (e) {
      logError("查找容器时出错", e, {
        element: element?.outerHTML,
        location: window.location.href,
      });
      return null;
    }
  }

  // 添加辅助函数：获取元素路径
  function getElementPath(element) {
    const path = [];
    let current = element;

    while (current) {
      let identifier = current.tagName.toLowerCase();
      if (current.id) {
        identifier += `#${current.id}`;
      } else if (current.className) {
        identifier += `.${Array.from(current.classList).join(".")}`;
      }
      path.unshift(identifier);
      current = current.parentElement;
    }

    return path.join(" > ");
  }

  // 隐藏容器
  function hideContainer(container) {
    if (!container || container.hasAttribute("data-hidden")) return;

    try {
      container.style.display = "none";
      container.setAttribute("data-hidden", "true");
      log("已隐藏容器:", container.id || container.className);
    } catch (e) {
      log("隐藏容器时出错:", e);
    }
  }

  // 隐藏帖子或帖子列表中的行
  function hidePostOrRow(element) {
    if (!element) return;

    try {
      // 检查是否为一楼或评分区域
      if (isFirstFloor(element)) {
        console.log("检测到一楼，拒绝隐藏操作");
        return;
      }

      if (element.closest(".ratelog")) {
        let ratelogItem = element.closest("dl");
        if (ratelogItem) {
          console.log("评分区域内的元素，只隐藏该评分行");
          ratelogItem.style.display = "none";
        }
        return;
      }

      // 隐藏帖子或帖子列表中的行
      let container = element.closest('div[id^="post_"]') || element.closest('table[id^="pid"]') || element.closest("tr") || element.closest(".threadlist_li") || element.closest(".thread");

      if (container && !isFirstFloor(container)) {
        container.style.display = "none";
      }
    } catch (e) {
      console.error("隐藏元素时出错:", e);
    }
  }

  // 修改 initializeExistingContent 函数
  function initializeExistingContent() {
    return measurePerformance("初始化内容", () => {
      log("初始化处理已存在的内容");
      try {
        // 首先处理所有标题
        let processedTitles = new Set();
        CONFIG.selectors.threadTitle.forEach((selector) => {
          document.querySelectorAll(selector).forEach((title) => {
            if (!title.hasAttribute("data-title-checked")) {
              handleThreadTitle(title);
              processedTitles.add(title);
            }
          });
        });

        if (processedTitles.size > 0) {
          log(`处理了 ${processedTitles.size} 个帖子标题`);
        }

        // 处理用户链接
        let processedLinks = new Set();
        let allLinks = new Set();
        CONFIG.selectors.authorLink.forEach((selector) => {
          document.querySelectorAll(selector).forEach((link) => allLinks.add(link));
        });

        log(`找到 ${allLinks.size} 个用户链接`);

        // 处理所有链接
        allLinks.forEach((link) => {
          if (!link.hasAttribute("data-handled") && !processedLinks.has(link)) {
            processedLinks.add(link);
            handleUserLink(link);
          }
        });

        log(`处理完成，共处理 ${processedLinks.size} 个用户链接`);
      } catch (e) {
        logError("初始化处理已存在内容时出错", e);
      }
    });
  }

  // 在 observer 之前调用初始化函数
  initializeExistingContent();

  // 修改 observer 配置
  var observer = new MutationObserver(function (mutations) {
    let addedNodes = new Set();

    for (let mutation of mutations) {
      if (mutation.addedNodes && mutation.addedNodes.length > 0) {
        mutation.addedNodes.forEach((node) => {
          if (node.nodeType === Node.ELEMENT_NODE) {
            addedNodes.add(node);
          }
        });
      }
    }

    if (addedNodes.size > 0) {
      logVerbose(`检测到 ${addedNodes.size} 个新增内容`);
      addedNodes.forEach((node) => {
        checkAndHide(node);
      });
    }
  });

  // 使用更精确的观察配置
  observer.observe(document.body, {
    childList: true,
    subtree: true,
    attributes: false,
    characterData: false,
  });

  // 添加一个按钮，用于显示和编辑屏蔽列表
  function addBlockListButton() {
    // 创建按钮
    var button = document.createElement("button");
    button.innerText = "屏蔽列表";
    button.className = "chh-block-button";

    // 点击按钮时弹出对话框
    button.addEventListener("click", function () {
      showBlockListDialog();
    });

    // 将按钮添加到页面
    document.body.appendChild(button);
  }

  // 显示屏蔽列表的对话框
  function showBlockListDialog() {
    // 创建对话框
    var dialog = document.createElement("div");
    dialog.className = "chh-dialog";

    // 创建标题栏
    var titleBar = document.createElement("div");
    titleBar.className = "chh-dialog-title";
    titleBar.textContent = "";

    // 添加拖拽功能变量
    var isDragging = false;
    var offsetX, offsetY;

    // 创建关闭按钮
    var closeButton = document.createElement("button");
    closeButton.innerText = "关闭";
    closeButton.className = "chh-close-button";

    // 添加十字线
    var closeLine1 = document.createElement("span");
    closeLine1.className = "chh-close-line chh-close-line-1";
    closeButton.appendChild(closeLine1);

    var closeLine2 = document.createElement("span");
    closeLine2.className = "chh-close-line chh-close-line-2";
    closeButton.appendChild(closeLine2);

    // 关闭对话框
    closeButton.addEventListener("click", function () {
      document.body.removeChild(dialog);
      document.removeEventListener("mousemove", onMouseMove);
      document.removeEventListener("mouseup", onMouseUp);
    });

    // 显示当前屏蔽的用户名（最新50个）
    var userList = document.createElement("div");
    userList.className = "chh-user-list";

    // 获取最新的50个用户名
    var recentUsers = blockedUsernames.slice(-50).reverse();
    var userItems = recentUsers.map((user) => '<span style="display: inline-block; margin: 0 8px 5px 0;">' + user + "</span>");
    userList.innerHTML = '<h3 class="chh-list-title">屏蔽的用户名 (最新50个):</h3>' + '<div class="chh-list-content">' + userItems.join("") + "</div>" + (blockedUsernames.length > 50 ? '<div class="chh-list-footer">...等共' + blockedUsernames.length + "个用户</div>" : "");

    // 创建添加用户名的容器
    var userInputContainer = document.createElement("div");
    userInputContainer.className = "chh-input-container";

    // 创建添加用户名的输入框和按钮
    var addUserInput = document.createElement("input");
    addUserInput.type = "text";
    addUserInput.placeholder = "输入用户名（区分大小写）";
    addUserInput.className = "chh-input";

    var addUserButton = document.createElement("button");
    addUserButton.innerText = "添加用户名";
    addUserButton.className = "chh-button";

    // 添加用户名
    addUserButton.addEventListener("click", function () {
      var newUsername = addUserInput.value.trim();
      if (newUsername && !blockedUsernames.includes(newUsername)) {
        blockedUsernames.push(newUsername);
        blockedUsernamesSet = new Set(blockedUsernames); // 更新Set
        updateUserList();
        addUserInput.value = ""; // 清空输入框
        saveBlockedLists(); // 保存到 localStorage

        // 重置所有已处理标记
        document.querySelectorAll("[data-handled]").forEach((el) => el.removeAttribute("data-handled"));
        document.querySelectorAll("[data-title-checked]").forEach((el) => el.removeAttribute("data-title-checked"));

        // 重新检查页面内容
        initializeExistingContent();
      }
    });

    // 更新用户列表显示的函数
    function updateUserList() {
      var recentUsers = blockedUsernames.slice(-50).reverse();
      var userItems = recentUsers.map((user) => '<span style="display: inline-block; margin: 0 8px 5px 0;">' + user + "</span>");
      userList.innerHTML = '<h3 class="chh-list-title">屏蔽的用户名 (最新50个):</h3>' + '<div class="chh-list-content">' + userItems.join("") + "</div>" + (blockedUsernames.length > 50 ? '<div class="chh-list-footer">...等共' + blockedUsernames.length + "个用户</div>" : "");
    }

    // 显示当前屏蔽的关键词（最新11个）
    var keywordList = document.createElement("div");
    keywordList.className = "chh-keyword-list";

    // 获取最新的11个关键词
    var recentKeywords = blockedKeywords.slice(-11).reverse();
    var keywordItems = recentKeywords.map((keyword) => '<span style="display: inline-block; margin: 0 8px 5px 0;">' + keyword + "</span>");
    keywordList.innerHTML = '<h3 class="chh-list-title">屏蔽的关键词 (最新11个):</h3>' + '<div class="chh-list-content">' + keywordItems.join("") + "</div>" + (blockedKeywords.length > 11 ? '<div class="chh-list-footer">...等共' + blockedKeywords.length + "个关键词</div>" : "");

    // 创建添加关键词的容器
    var keywordInputContainer = document.createElement("div");
    keywordInputContainer.className = "chh-input-container";

    // 创建添加关键词的输入框和按钮
    var addKeywordInput = document.createElement("input");
    addKeywordInput.type = "text";
    addKeywordInput.placeholder = "输入关键词";
    addKeywordInput.className = "chh-input";

    var addKeywordButton = document.createElement("button");
    addKeywordButton.innerText = "添加关键词";
    addKeywordButton.className = "chh-button";

    // 添加关键词
    addKeywordButton.addEventListener("click", function () {
      var newKeyword = addKeywordInput.value.trim();
      if (newKeyword && !blockedKeywords.includes(newKeyword)) {
        blockedKeywords.push(newKeyword);
        // 更新关键词列表显示
        updateKeywordList();
        addKeywordInput.value = ""; // 清空输入框
        saveBlockedLists(); // 保存到 localStorage
        preProcessAllRatelogItems(); // 重新检查页面内容
      }
    });

    // 更新关键词列表显示的函数
    function updateKeywordList() {
      var recentKeywords = blockedKeywords.slice(-11).reverse();
      var keywordItems = recentKeywords.map((keyword) => '<span style="display: inline-block; margin: 0 8px 5px 0;">' + keyword + "</span>");
      keywordList.innerHTML = '<h3 class="chh-list-title">屏蔽的关键词 (最新11个):</h3>' + '<div class="chh-list-content">' + keywordItems.join("") + "</div>" + (blockedKeywords.length > 11 ? '<div class="chh-list-footer">...等共' + blockedKeywords.length + "个关键词</div>" : "");
    }

    // 将元素添加到对话框
    dialog.appendChild(titleBar);
    dialog.appendChild(closeButton);
    dialog.appendChild(userList);

    // 添加用户名输入区域 - 移到屏蔽列表和屏蔽关键词之间
    userInputContainer.appendChild(addUserInput);
    userInputContainer.appendChild(addUserButton);
    dialog.appendChild(userInputContainer);

    dialog.appendChild(keywordList);

    // 添加关键词输入区域
    keywordInputContainer.appendChild(addKeywordInput);
    keywordInputContainer.appendChild(addKeywordButton);
    dialog.appendChild(keywordInputContainer);

    // 添加拖拽功能，只能通过标题栏拖动
    titleBar.addEventListener("mousedown", function (e) {
      isDragging = true;

      // 获取鼠标在对话框内的位置
      var rect = dialog.getBoundingClientRect();
      offsetX = e.clientX - rect.left;
      offsetY = e.clientY - rect.top;

      // 移除原来的transform属性，使用left和top定位
      dialog.style.transform = "none";
      dialog.style.left = rect.left + "px";
      dialog.style.top = rect.top + "px";

      // 防止选中文本
      e.preventDefault();
    });

    // 定义事件处理函数，使其可在外部引用以便移除
    function onMouseMove(e) {
      if (isDragging) {
        var newLeft = e.clientX - offsetX;
        var newTop = e.clientY - offsetY;

        // 防止对话框拖出屏幕边界
        var maxX = window.innerWidth - dialog.offsetWidth;
        var maxY = window.innerHeight - dialog.offsetHeight;

        dialog.style.left = Math.max(0, Math.min(newLeft, maxX)) + "px";
        dialog.style.top = Math.max(0, Math.min(newTop, maxY)) + "px";
      }
    }

    function onMouseUp() {
      isDragging = false;
    }

    // 添加全局事件监听器
    document.addEventListener("mousemove", onMouseMove);
    document.addEventListener("mouseup", onMouseUp);

    // 将对话框添加到页面
    document.body.appendChild(dialog);
  }

  // 添加按钮到页面
  addBlockListButton();

  // 添加到window对象，方便用户控制
  window.CHHDebug = {
    enable: (verbose = false) => setDebugMode(true, verbose),
    disable: () => setDebugMode(false, false),
    status: () => ({
      debug: CONFIG.debug,
      verbose: CONFIG.debugVerbose,
      bufferSize: CONFIG.debugOptions.logBuffer.length,
    }),
  };
})();
