/**
 * 导航菜单拖拽排序功能测试脚本
 * 
 * 本脚本用于测试导航菜单拖拽排序功能的修复效果。
 * 使用方法：
 * 1. 在浏览器控制台中运行此脚本
 * 2. 按照提示进行测试
 */

// 测试数据
const testBoards = [
    { name: "测试板块1", url: "/forum.php?mod=forumdisplay&fid=1" },
    { name: "测试板块2", url: "/forum.php?mod=forumdisplay&fid=2" },
    { name: "测试板块3", url: "/forum.php?mod=forumdisplay&fid=3" },
    { name: "测试板块4", url: "/forum.php?mod=forumdisplay&fid=4" },
    { name: "测试板块5", url: "/forum.php?mod=forumdisplay&fid=5" }
];

// 测试函数
function testDragSort() {
    console.log("开始测试导航菜单拖拽排序功能...");
    
    // 测试1：创建菜单
    console.log("测试1：创建菜单");
    const menu = createNavigationMenu(testBoards);
    document.body.appendChild(menu);
    console.log("菜单创建成功");
    
    // 测试2：检查拖拽事件是否正确添加
    console.log("测试2：检查拖拽事件是否正确添加");
    const items = menu.querySelectorAll('.bgsh-navigation-item');
    const dragHandles = menu.querySelectorAll('.bgsh-drag-handle');
    console.log(`找到 ${items.length} 个菜单项和 ${dragHandles.length} 个拖拽手柄`);
    
    // 测试3：模拟拖拽操作
    console.log("测试3：模拟拖拽操作");
    console.log("请手动测试拖拽功能，并观察以下行为：");
    console.log("- 拖拽时是否显示占位符");
    console.log("- 拖拽结束后是否正确更新排序");
    console.log("- 刷新页面后是否保持排序");
    
    // 测试4：测试保存和应用排序
    console.log("测试4：测试保存和应用排序");
    const testUrls = testBoards.map(board => board.url).reverse();
    saveSortOrder(testUrls);
    console.log("排序已保存，顺序已反转");
    
    const sortedBoards = applySortOrder(testBoards);
    console.log("应用排序结果：", sortedBoards.map(board => board.name));
    
    // 测试5：测试错误处理
    console.log("测试5：测试错误处理");
    try {
        // 故意传入错误参数
        saveSortOrder(null);
        console.log("错误处理测试通过：saveSortOrder 不会抛出异常");
    } catch (e) {
        console.error("错误处理测试失败：saveSortOrder 抛出异常", e);
    }
    
    try {
        // 故意传入错误参数
        applySortOrder(null);
        console.log("错误处理测试通过：applySortOrder 不会抛出异常");
    } catch (e) {
        console.error("错误处理测试失败：applySortOrder 抛出异常", e);
    }
    
    console.log("测试完成，请手动验证功能是否正常工作");
}

// 运行测试
testDragSort();