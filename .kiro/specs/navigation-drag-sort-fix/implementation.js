/**
 * 导航菜单拖拽排序功能修复
 * 
 * 本文件包含修复导航菜单拖拽排序功能的代码实现。
 * 主要解决了以下问题：
 * 1. 拖拽事件处理混乱
 * 2. 占位符处理不当
 * 3. 拖拽状态管理不完善
 * 4. 事件委托不足
 * 5. 调试代码过多
 */

// 拖拽状态管理对象
const dragState = {
    draggedElement: null,
    placeholder: null,
    originalPosition: null,
    
    // 开始拖拽
    start(element) {
        this.draggedElement = element;
        this.originalPosition = this.getElementIndex(element);
        element.classList.add('bgsh-dragging');
    },
    
    // 结束拖拽
    end() {
        if (this.draggedElement) {
            this.draggedElement.classList.remove('bgsh-dragging');
        }
        
        if (this.placeholder && this.placeholder.parentNode) {
            this.placeholder.remove();
        }
        
        this.draggedElement = null;
        this.placeholder = null;
        this.originalPosition = null;
    },
    
    // 获取元素在父容器中的索引
    getElementIndex(element) {
        return Array.from(element.parentNode.children).indexOf(element);
    },
    
    // 创建占位符
    createPlaceholder() {
        const placeholder = document.createElement('div');
        placeholder.className = 'bgsh-drag-placeholder';
        placeholder.textContent = '放置在这里';
        this.placeholder = placeholder;
        return placeholder;
    },
    
    // 定位占位符
    positionPlaceholder(target, position) {
        if (!this.placeholder || !target) return;
        
        // 如果占位符已经在正确位置，不做任何操作
        if (position === 'before' && this.placeholder.nextElementSibling === target) return;
        if (position === 'after' && this.placeholder.previousElementSibling === target) return;
        
        if (position === 'before') {
            target.parentNode.insertBefore(this.placeholder, target);
        } else {
            target.parentNode.insertBefore(this.placeholder, target.nextElementSibling);
        }
    }
};

/**
 * 包装事件处理函数，添加错误处理
 * @param {Function} handler - 原始事件处理函数
 * @returns {Function} 包装后的事件处理函数
 */
function wrapEventHandler(handler) {
    return function(event) {
        try {
            handler.call(this, event);
        } catch (error) {
            console.error('拖拽事件处理错误:', error);
            // 恢复到安全状态
            dragState.end();
            // 防止事件继续传播
            event.preventDefault();
            event.stopPropagation();
        }
    };
}

/**
 * 为菜单项添加拖拽事件
 * @param {HTMLElement} item - 菜单项元素
 * @param {HTMLElement} list - 列表容器元素
 */
function addDragEvents(item, list) {
    // 只在拖拽手柄上启用拖拽
    const dragHandle = item.querySelector('.bgsh-drag-handle');
    if (!dragHandle) return;
    
    // 拖拽开始 - 在拖拽手柄上监听
    dragHandle.addEventListener('dragstart', wrapEventHandler((e) => {
        dragState.start(item);
        
        // 设置拖拽数据
        e.dataTransfer.effectAllowed = 'move';
        e.dataTransfer.setData('text/plain', item.dataset.boardUrl);
        
        // 创建并定位占位符（使用setTimeout确保在拖拽开始后执行）
        setTimeout(() => {
            const placeholder = dragState.createPlaceholder();
            dragState.positionPlaceholder(item, 'after');
        }, 0);
    }));
    
    // 拖拽结束 - 在拖拽手柄上监听
    dragHandle.addEventListener('dragend', wrapEventHandler(() => {
        // 保存新的排序
        const items = list.querySelectorAll('.bgsh-navigation-item');
        const sortedUrls = Array.from(items).map(item => item.dataset.boardUrl);
        saveSortOrder(sortedUrls);
        
        // 清理拖拽状态
        dragState.end();
    }));
    
    // 拖拽经过 - 在菜单项上监听
    item.addEventListener('dragover', wrapEventHandler((e) => {
        e.preventDefault();
        e.dataTransfer.dropEffect = 'move';
        
        if (!dragState.draggedElement || dragState.draggedElement === item) return;
        
        const rect = item.getBoundingClientRect();
        const midY = rect.top + rect.height / 2;
        
        // 根据鼠标位置决定占位符位置
        if (e.clientY < midY) {
            dragState.positionPlaceholder(item, 'before');
        } else {
            dragState.positionPlaceholder(item, 'after');
        }
    }));
    
    // 放置 - 在菜单项上监听
    item.addEventListener('drop', wrapEventHandler((e) => {
        e.preventDefault();
        
        if (dragState.draggedElement && dragState.placeholder) {
            // 将拖拽的元素移动到占位符位置
            list.insertBefore(dragState.draggedElement, dragState.placeholder);
        }
    }));
    
    // 为拖拽手柄添加特殊样式
    dragHandle.addEventListener('mousedown', () => {
        dragHandle.style.cursor = 'grabbing';
    });
    
    dragHandle.addEventListener('mouseup', () => {
        dragHandle.style.cursor = 'grab';
    });
}

/**
 * 设置列表容器的事件处理
 * @param {HTMLElement} list - 列表容器元素
 */
function setupListEventHandlers(list) {
    // 防止重复添加事件
    if (list._eventHandlersAdded) return;
    
    // 拖拽经过列表容器
    list.addEventListener('dragover', wrapEventHandler((e) => {
        e.preventDefault();
        
        // 如果拖拽到了列表容器的空白区域
        if (e.target === list && dragState.draggedElement) {
            // 将占位符添加到列表末尾
            dragState.positionPlaceholder(list.lastElementChild, 'after');
        }
    }));
    
    // 放置到列表容器
    list.addEventListener('drop', wrapEventHandler((e) => {
        e.preventDefault();
        
        // 如果放置到了列表容器的空白区域
        if (e.target === list && dragState.draggedElement && dragState.placeholder) {
            // 将拖拽的元素移动到列表末尾
            list.appendChild(dragState.draggedElement);
        }
    }));
    
    list._eventHandlersAdded = true;
}

/**
 * 保存用户自定义的排序
 * @param {Array} sortedUrls - 排序后的URL数组
 * @returns {boolean} 保存是否成功
 */
function saveSortOrder(sortedUrls) {
    try {
        // 添加版本信息和时间戳，便于未来兼容性处理
        const sortData = {
            version: 1,
            timestamp: Date.now(),
            urls: sortedUrls
        };
        
        GM_setValue('favorite_boards_sort_order', JSON.stringify(sortData));
        return true;
    } catch (e) {
        console.error('保存排序失败:', e);
        return false;
    }
}

/**
 * 应用用户自定义的排序
 * @param {Array} boards - 原始板块列表
 * @returns {Array} 排序后的板块列表
 */
function applySortOrder(boards) {
    try {
        const savedData = GM_getValue('favorite_boards_sort_order', '{}');
        let sortOrder;
        
        try {
            const parsedData = JSON.parse(savedData);
            // 兼容旧版数据格式
            sortOrder = Array.isArray(parsedData) ? parsedData : (parsedData.urls || []);
        } catch {
            sortOrder = [];
        }
        
        if (sortOrder.length === 0) {
            return boards;
        }
        
        // 按照保存的顺序重新排列
        const sortedBoards = [];
        const remainingBoards = [...boards];
        
        // 先按保存的顺序添加
        sortOrder.forEach(savedUrl => {
            const index = remainingBoards.findIndex(board => board.url === savedUrl);
            if (index !== -1) {
                sortedBoards.push(remainingBoards.splice(index, 1)[0]);
            }
        });
        
        // 添加新增的板块（不在保存的顺序中）
        return sortedBoards.concat(remainingBoards);
    } catch (e) {
        console.error('应用排序失败:', e);
        return boards;
    }
}

/**
 * 创建导航菜单元素
 * @param {Array} boards - 收藏的板块列表
 * @return {HTMLElement} 菜单元素
 */
function createNavigationMenu(boards) {
    const menu = document.createElement("div");
    menu.className = "bgsh-navigation-menu";

    const title = document.createElement("div");
    title.className = "bgsh-navigation-title";
    title.innerHTML = `
        <span>收藏的板块</span>
        <span class="bgsh-drag-hint">拖动排序</span>
    `;
    menu.appendChild(title);

    const list = document.createElement("div");
    list.className = "bgsh-navigation-list";

    // 设置列表容器的事件处理
    setupListEventHandlers(list);

    // 应用用户自定义排序
    const sortedBoards = applySortOrder(boards);

    sortedBoards.forEach((board, index) => {
        const item = document.createElement("div");
        item.className = "bgsh-navigation-item";
        item.dataset.boardUrl = board.url;
        item.dataset.boardName = board.name;
        item.dataset.originalIndex = index;

        // 创建拖拽手柄和内容
        item.innerHTML = `
            <span class="bgsh-drag-handle" draggable="true">⋮⋮</span>
            <span class="bgsh-item-text">${board.name}</span>
        `;
        item.title = `${board.url}\n拖动可重新排序`; // 添加悬停提示

        // 添加拖拽事件
        addDragEvents(item, list);

        // 添加点击事件（避免与拖拽冲突）
        item.addEventListener('click', (e) => {
            // 如果点击的是拖拽手柄，不执行跳转
            if (e.target.classList.contains('bgsh-drag-handle')) {
                e.preventDefault();
                e.stopPropagation();
                return;
            }

            e.preventDefault();
            e.stopPropagation();

            // 添加点击反馈
            item.style.backgroundColor = 'rgba(40, 167, 69, 0.2)';
            setTimeout(() => {
                window.location.href = board.url;
            }, 150);
        });

        // 添加键盘导航支持
        item.setAttribute('tabindex', '0');
        item.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                item.click();
            }
        });

        list.appendChild(item);
    });

    menu.appendChild(list);

    // 添加显示动画
    setTimeout(() => {
        menu.classList.add('bgsh-show');
    }, 10);

    return menu;
}

/**
 * 添加触摸支持（用于移动设备）
 * @param {HTMLElement} menu - 导航菜单元素
 */
function addTouchSupport(menu) {
    const items = menu.querySelectorAll('.bgsh-navigation-item');
    let touchStartY, touchStartItem, touchStartIndex;
    
    items.forEach(item => {
        const dragHandle = item.querySelector('.bgsh-drag-handle');
        
        dragHandle.addEventListener('touchstart', (e) => {
            e.preventDefault();
            touchStartY = e.touches[0].clientY;
            touchStartItem = item;
            touchStartIndex = Array.from(items).indexOf(item);
            
            item.classList.add('bgsh-dragging');
        });
        
        item.addEventListener('touchmove', (e) => {
            if (!touchStartItem) return;
            
            const touchY = e.touches[0].clientY;
            const deltaY = touchY - touchStartY;
            const itemHeight = item.offsetHeight;
            
            // 计算应该移动的位置
            let newIndex = touchStartIndex + Math.round(deltaY / itemHeight);
            newIndex = Math.max(0, Math.min(newIndex, items.length - 1));
            
            if (newIndex !== touchStartIndex) {
                const list = item.parentNode;
                const targetItem = items[newIndex];
                
                if (newIndex < touchStartIndex) {
                    list.insertBefore(touchStartItem, targetItem);
                } else {
                    list.insertBefore(touchStartItem, targetItem.nextElementSibling);
                }
                
                // 更新索引和起始位置
                touchStartIndex = newIndex;
                touchStartY = touchY;
            }
        });
        
        dragHandle.addEventListener('touchend', () => {
            if (!touchStartItem) return;
            
            touchStartItem.classList.remove('bgsh-dragging');
            
            // 保存新的排序
            const list = item.parentNode;
            const sortedItems = list.querySelectorAll('.bgsh-navigation-item');
            const sortedUrls = Array.from(sortedItems).map(item => item.dataset.boardUrl);
            saveSortOrder(sortedUrls);
            
            // 重置触摸状态
            touchStartY = null;
            touchStartItem = null;
            touchStartIndex = null;
        });
    });
}

// 导出函数，供主脚本使用
return {
    dragState,
    addDragEvents,
    setupListEventHandlers,
    saveSortOrder,
    applySortOrder,
    createNavigationMenu,
    addTouchSupport
};