# Implementation Plan

- [x] 1. Modify the exportAllConfigs function to include preset order
  - Add preset order information to the exported data structure
  - Ensure backward compatibility with existing code
  - _Requirements: 1.1, 1.2_

- [x] 2. Modify the importAllConfigs function to handle preset order
  - Extract preset order information from imported data
  - Save the order using savePresetOrder function
  - Handle cases where order information is missing (backward compatibility)
  - _Requirements: 1.3, 1.5, 2.1_

- [x] 3. Update the import process for new theme IDs
  - Ensure imported themes get new IDs that are properly tracked in the order
  - Map old IDs to new IDs in the order array
  - _Requirements: 1.3, 2.1_

- [x] 4. Implement order validation and cleanup
  - Remove non-existent theme IDs from the order array
  - Add missing theme IDs to the end of the order array
  - _Requirements: 1.5, 2.2, 2.3_

- [x] 5. Test the implementation
  - Test drag and drop sorting functionality
  - Test exporting themes after reordering
  - Test importing themes and verifying order is preserved
  - Test backward compatibility with exports from older versions
  - _Requirements: 1.1, 1.2, 1.3, 1.4, 1.5, 2.1_