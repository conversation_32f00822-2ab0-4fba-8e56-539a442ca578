# Test Plan for Theme Sorting Fix

## Test Cases

### 1. <PERSON><PERSON> and Drop Sorting

1. Open the theme editor panel
2. Create multiple themes (at least 3)
3. Drag and reorder the themes in different ways
4. Verify that the order is visually updated correctly
5. Close and reopen the panel to verify the order is saved

### 2. Export After Reordering

1. Reorder themes using drag and drop
2. Export all themes
3. Examine the exported JSON file to verify it contains the `preset_order` array
4. Verify that the order in the `preset_order` array matches the visual order in the panel

### 3. Import and Order Preservation

1. Export themes after reordering
2. Clear browser storage or use a different browser
3. Import the exported themes
4. Verify that the imported themes appear in the same order as they were exported

### 4. Backward Compatibility

1. Create a modified export file without the `preset_order` property
2. Import this file
3. Verify that the themes are imported successfully
4. Verify that the themes are displayed in a reasonable order

### 5. Edge Cases

1. **Single Theme**: Test with only one custom theme
2. **Many Themes**: Test with a large number of themes (10+)
3. **Default Theme**: Verify that the default theme is always first
4. **Theme Deletion**: Delete a theme and verify the order is updated correctly
5. **New Theme Addition**: Add a new theme and verify it's added to the end of the order

## Expected Results

- The drag-and-drop sorting functionality should work correctly
- The order should be preserved when exporting and importing themes
- The implementation should be backward compatible with exports from older versions
- Edge cases should be handled gracefully

## Test Environment

- Test in multiple browsers (Chrome, Firefox, Safari)
- Test with different numbers of themes
- Test with different theme configurations