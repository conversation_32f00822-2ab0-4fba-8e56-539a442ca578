// ==UserScript==
// @name         贴吧内容屏蔽
// @namespace    http://tampermonkey.net/
// @version      2.2.0
// @description  贴吧用户屏蔽脚本，基于用户ID精确屏蔽，统一用户ID格式处理，去除时间戳参数
// <AUTHOR> name
// @match        *://tieba.baidu.com/*
// @grant        GM_getValue
// @grant        GM_setValue
// ==/UserScript==

(function () {
	"use strict";

	// 添加样式
	const style = document.createElement("style");
	style.textContent = `
.block-list-button {
	position: fixed;
	bottom: 20px;
	right: 20px;
	z-index: 1000;
	padding: 5px 12px;
	background: #000;
	box-shadow: 0 0 6px rgba(0, 0, 0, 0.2);
	color: #fff;
	border: none;
	border-radius: 10px;
	cursor: pointer;
	opacity: 0;
	transition: opacity 0.3s ease;
	pointer-events: none;
	font-size: 13px;
	font-weight: bold !important;
}

.block-dialog {
	position: fixed;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	width: 600px;
	max-width: 90vw;
	height: auto;
	max-height: 80vh;
	padding: 20px;
	border-radius: 10px;
	z-index: 1001;
	overflow: auto;
	background: rgba(250, 250, 252, 0.8);
	border: 1px solid rgba(255, 255, 255, 0.7);
	backdrop-filter: blur(30px) saturate(180%);
	box-shadow: -5px 1px 50px rgba(0, 0, 0, 0.20), inset 0px 0px 30px rgba(255, 255, 255, 0.9);
}

.block-title-bar {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 30px;
    background: none;
    cursor: move;
    user-select: none;
}

.block-close-button {
    position: absolute;
    top: 12px;
    right: 12px;
    width: 16px;
    height: 16px;
    background: black;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    transition: background 0.15s ease;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
    overflow: hidden;
    text-indent: -9999px;
    padding: 0;
    z-index: 1002;
}

.block-close-button:hover {
    background: red;
}

.block-close-line {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 10px;
    height: 2px;
    background: white;
    transition: background 0.15s ease;
}

.block-close-line-1 {
    transform: translate(-50%, -50%) rotate(45deg);
}

.block-close-line-2 {
    transform: translate(-50%, -50%) rotate(-45deg);
}

.block-list {
	margin: 20px 0 0 0;
	max-height: 220px;
	font-size: 12px;
	padding: 10px;
	border-radius: 8px;
	background: rgb(131, 131, 145, 0.15);
}

.block-keyword-list {
    margin: 15px 0 0 0;
    max-height: 100px;
}

.block-input-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 15px 0;
    width: 100%;
}

.block-input-group {
    display: flex;
    align-items: center;
    width: 45%;
}

.keyword-input-group {
    display: flex;
    align-items: center;
    width: 50%;
    margin: 0 auto;
}

.block-input {
	flex: 1;
	border: none;
	border-radius: 8px;
	font-size: 13px;
	padding: 5px 10px;
	margin-right: 10px;
	min-width: 0;
	background: rgb(131, 131, 145, 0.15);
}

.block-input:focus {
	outline: 1px solid rgba(255, 255, 255, 0.2);
	background: rgb(131, 131, 145, 0.2);
}

.block-button {
    border-radius: 8px;
    border: 0px solid black;
    background: #000;
    padding: 5px 10px;
    color: white;
    font-size: 13px;
    cursor: pointer;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
    white-space: nowrap;
    min-width: fit-content;
}

.trigger-area {
    position: fixed;
    bottom: 0;
    right: 0;
    width: 100px;
    height: 100px;
    z-index: 999;
}

.trigger-area:hover .block-list-button {
    opacity: 1;
    pointer-events: auto;
}

/* 悬停屏蔽按钮样式 */
.hover-block-button {
	position: absolute;
	background: #ff4444;
	color: white;
	border: none;
	border-radius: 8px;
	padding: 1px 10px;
	font-size: 12px;
	cursor: pointer;
	z-index: 10000;
	box-shadow: 0 1px 8px rgba(0, 0, 0, 0.2);
	white-space: nowrap;
	transition: background 0.2s ease;
}
/* 用户名悬停容器 */
.username-hover-container {
    position: relative;
    display: inline-block;
}
    `;
	document.head.appendChild(style);

	// 存储类定义
	class I {
		constructor(name, defaultValue, storage) {
			this.name = name;
			this.defaultValue = defaultValue;
			this.storage = storage;
		}

		get() {
			return this.storage.getValue(this.name, this.defaultValue);
		}

		set(value) {
			this.storage.setValue(this.name, value);
		}

		remove() {
			this.set([]);
		}
	}

	// 初始化存储
	const mt = new I("shieldList", [], {
		getValue: GM_getValue,
		setValue: GM_setValue,
	});

	// 统一的数据结构 - 扩展原有的shieldList格式
	// 新格式支持：
	// 用户: { type: "user", content: { id: "用户ID", name: "显示名" } }
	// 关键词: { type: "keyword", content: "关键词文本" }
	// 兼容旧格式: { type: "user", content: "用户名字符串" }

	let storedData = mt.get() || [];

	// 解析用户数据
	let blockedUsers = []; // 保持兼容性的简单用户名列表
	let blockedUserProfiles = []; // 新的用户配置文件列表

	storedData.filter(item => item.type === "user").forEach(item => {
		if (typeof item.content === "object" && item.content !== null) {
			// 新格式：{ id: "用户ID", name: "显示名" }
			if (item.content.id && item.content.name) {
				blockedUserProfiles.push(item.content);
				blockedUsers.push(item.content.name); // 添加到兼容列表
			} else {
				// 处理其他对象格式
				const userName = item.content.content || item.content.toString();
				blockedUsers.push(userName);
			}
		} else {
			// 旧格式：字符串
			blockedUsers.push(item.content.toString());
		}
	});

	// 解析关键词数据
	let blockedKeywords = storedData
		.filter((item) => item.type === "keyword")
		.map((item) => {
			if (typeof item.content === "object" && item.content !== null) {
				return item.content.content || item.content.toString();
			}
			return item.content.toString();
		});

	// 辅助函数：添加用户到屏蔽列表
	function addUserToBlockList(userId, showName) {
		// 检查是否已存在（优先检查ID）
		if (userId) {
			const existingProfile = blockedUserProfiles.find(profile => profile.id === userId);
			if (existingProfile) {
				// 更新显示名称（可能用户改名了）
				existingProfile.name = showName;
				return false; // 表示没有添加新用户
			}
		}

		// 检查显示名是否已存在
		if (showName && blockedUsers.includes(showName)) {
			return false; // 已存在
		}

		// 添加新用户到统一数据结构
		if (userId && showName) {
			// 有完整信息，使用新格式
			const userProfile = { id: userId, name: showName };
			blockedUserProfiles.push(userProfile);
			blockedUsers.push(showName); // 保持兼容性
		} else if (showName) {
			// 只有显示名，使用旧格式
			blockedUsers.push(showName);
		}

		return true; // 表示添加了新用户
	}

	// 辅助函数：检查用户是否被屏蔽
	function isUserBlocked(userId, showName) {
		// 优先检查用户ID
		if (userId && blockedUserProfiles.some(profile => profile.id === userId)) {
			return true;
		}

		// 回退检查显示名称
		if (showName && blockedUsers.includes(showName)) {
			return true;
		}

		return false;
	}

	// 统一保存函数
	function saveBlockedLists() {
		const combinedList = [];

		// 保存用户配置文件（新格式）
		blockedUserProfiles.forEach(profile => {
			combinedList.push({
				type: "user",
				content: { id: profile.id, name: profile.name }
			});
		});

		// 保存只有显示名的用户（旧格式兼容）
		const profileNames = blockedUserProfiles.map(p => p.name);
		blockedUsers.forEach(userName => {
			if (!profileNames.includes(userName)) {
				combinedList.push({
					type: "user",
					content: userName.toString()
				});
			}
		});

		// 保存关键词
		blockedKeywords.forEach(keyword => {
			combinedList.push({
				type: "keyword",
				content: keyword.toString()
			});
		});

		mt.set(combinedList);
	}

	// 添加屏蔽按钮
	function addBlockListButton() {
		const button = document.createElement("button");
		button.innerText = "屏蔽列表";
		button.className = "block-list-button";
		button.addEventListener("click", showBlockListDialog);
		return button;
	}
	// 创建触发区域
	const triggerArea = document.createElement("div");
	triggerArea.className = "trigger-area";
	triggerArea.appendChild(addBlockListButton());
	document.body.appendChild(triggerArea);
	// 显示屏蔽列表对话框
	function showBlockListDialog() {
		const dialog = document.createElement("div");
		dialog.className = "block-dialog";

		// 创建标题栏作为拖动区域
		const titleBar = document.createElement("div");
		titleBar.className = "block-title-bar";

		// 拖动实现
		let isDragging = false;
		let startX, startY;
		let initialLeft, initialTop;

		function startDragging(e) {
			if (e.target === titleBar) {
				isDragging = true;
				startX = e.clientX;
				startY = e.clientY;

				const rect = dialog.getBoundingClientRect();
				initialLeft = rect.left;
				initialTop = rect.top;

				dialog.style.transform = "none";
				dialog.style.left = `${initialLeft}px`;
				dialog.style.top = `${initialTop}px`;
			}
		}

		function doDrag(e) {
			if (!isDragging) return;

			e.preventDefault();
			const deltaX = e.clientX - startX;
			const deltaY = e.clientY - startY;

			dialog.style.left = `${initialLeft + deltaX}px`;
			dialog.style.top = `${initialTop + deltaY}px`;
		}

		function stopDragging() {
			isDragging = false;
		}

		titleBar.addEventListener("mousedown", startDragging);
		document.addEventListener("mousemove", doDrag);
		document.addEventListener("mouseup", stopDragging);

		// 创建关闭按钮
		const closeButton = document.createElement("button");
		closeButton.className = "block-close-button";

		// 创建关闭按钮的X图标
		const before = document.createElement("span");
		before.className = "block-close-line block-close-line-1";

		const after = document.createElement("span");
		after.className = "block-close-line block-close-line-2";

		closeButton.appendChild(before);
		closeButton.appendChild(after);

		closeButton.onclick = () => document.body.removeChild(dialog);

		// 显示当前屏蔽的用户（优先显示用户配置文件，回退到旧列表）
		const userList = document.createElement("div");
		userList.className = "block-list";

		// 合并新旧数据，优先显示新的用户配置文件
		const allUsers = [];

		// 添加新的用户配置文件（显示名称）
		blockedUserProfiles.forEach(profile => {
			if (profile.name) {
				allUsers.push(profile.name);
			}
		});

		// 添加旧的用户列表中不重复的项
		blockedUsers.forEach(user => {
			if (!allUsers.includes(user)) {
				allUsers.push(user);
			}
		});

		const recentUsers = allUsers.slice(-50).reverse();
		userList.innerHTML =
			`<h3>屏蔽的用户 (最新${recentUsers.length}个):</h3>` +
			recentUsers.join(", ") +
			(allUsers.length > 50
				? `<div style="color: #666; margin-top: 5px;">...等共${allUsers.length}个用户</div>`
				: "");

		// 创建用户ID输入区域容器
		const userInputContainer = document.createElement("div");
		userInputContainer.className = "block-input-container";
		userInputContainer.style.justifyContent = "center";

		// 创建用户ID输入组
		const userIdInputGroup = document.createElement("div");
		userIdInputGroup.className = "block-input-group";

		// 创建添加用户ID的输入框和按钮
		const addUserIdInput = document.createElement("input");
		addUserIdInput.type = "text";
		addUserIdInput.placeholder = "输入用户ID";
		addUserIdInput.className = "block-input";

		// 添加获取焦点和失去焦点事件
		addUserIdInput.addEventListener('focus', function() {
			this.placeholder = '';
		});
		addUserIdInput.addEventListener('blur', function() {
			if (!this.value) {
				this.placeholder = '输入用户ID';
			}
		});

		const addUserIdButton = document.createElement("button");
		addUserIdButton.innerText = "添加用户ID";
		addUserIdButton.className = "block-button";

		// 用户ID格式化函数
		const formatUserId = (userId) => {
			if (!userId) return null;

			// 去除时间戳参数
			if (userId.includes('?t=')) {
				userId = userId.split('?t=')[0];
			}

			// 验证用户ID格式是否正确 (tb.数字.字符串.字符串)
			const userIdPattern = /^tb\.\d+\.[a-f0-9]+\.[a-zA-Z0-9_-]+$/;
			if (userIdPattern.test(userId)) {
				return userId;
			}

			return null;
		};

		// 添加用户ID事件处理
		addUserIdButton.onclick = () => {
			const inputUserId = addUserIdInput.value.trim();
			if (inputUserId) {
				// 格式化用户ID
				const formattedUserId = formatUserId(inputUserId);

				if (formattedUserId) {
					// 使用格式化后的用户ID
					const isNewUser = addUserToBlockList(formattedUserId, `用户ID:${formattedUserId.substring(0, 10)}...`);
					if (isNewUser) {
						updateUserList();
						addUserIdInput.value = "";
						saveBlockedLists();
						hideBlockedContent();
					}
				} else {
					// 显示错误提示
					alert('用户ID格式不正确！\n正确格式示例：tb.1.a51cbeeb.v7lrS0EbIszf6lJnXUeYqw');
				}
			}
		};

		// 更新用户列表显示的函数
		function updateUserList() {
			// 合并新旧数据
			const allUsers = [];

			// 添加新的用户配置文件（显示名称）
			blockedUserProfiles.forEach(profile => {
				if (profile.name) {
					allUsers.push(profile.name);
				}
			});

			// 添加旧的用户列表中不重复的项
			blockedUsers.forEach(user => {
				if (!allUsers.includes(user)) {
					allUsers.push(user);
				}
			});

			const recentUsers = allUsers.slice(-50).reverse();
			userList.innerHTML =
				`<h3>屏蔽的用户 (最新${recentUsers.length}个):</h3>` +
				recentUsers.join(", ") +
				(allUsers.length > 50
					? `<div style="color: #666; margin-top: 5px;">...等共${allUsers.length}个用户</div>`
					: "");
		}

		// 显示当前屏蔽的关键词（最新50个）
		const keywordList = document.createElement("div");
		keywordList.className = "block-list block-keyword-list";
		const recentKeywords = blockedKeywords.slice(-50).reverse();
		keywordList.innerHTML =
			`<h3>屏蔽的关键词 (最新${recentKeywords.length}个):</h3>` +
			recentKeywords.join(", ") +
			(blockedKeywords.length > 50
				? `<div style="color: #666; margin-top: 5px;">...等共${blockedKeywords.length}个关键词</div>`
				: "");

		// 创建关键词输入区域容器
		const keywordInputContainer = document.createElement("div");
		keywordInputContainer.className = "block-input-container";
		keywordInputContainer.style.justifyContent = "center";

		// 创建关键词输入组
		const keywordInputGroup = document.createElement("div");
		keywordInputGroup.className = "keyword-input-group";

		// 创建添加关键词的输入框和按钮
		const addKeywordInput = document.createElement("input");
		addKeywordInput.type = "text";
		addKeywordInput.placeholder = "输入关键词";
		addKeywordInput.className = "block-input";

		// 添加获取焦点和失去焦点事件
		addKeywordInput.addEventListener('focus', function() {
			this.placeholder = '';
		});
		addKeywordInput.addEventListener('blur', function() {
			if (!this.value) {
				this.placeholder = '输入关键词';
			}
		});

		const addKeywordButton = document.createElement("button");
		addKeywordButton.innerText = "添加关键词";
		addKeywordButton.className = "block-button";

		// 添加关键词事件处理
		addKeywordButton.onclick = () => {
			const newKeyword = addKeywordInput.value.trim();
			if (newKeyword && !blockedKeywords.includes(newKeyword)) {
				blockedKeywords.push(newKeyword);
				updateKeywordList();
				addKeywordInput.value = "";
				saveBlockedLists();
				hideBlockedContent();
			}
		};

		// 更新关键词列表显示的函数
		function updateKeywordList() {
			const recentKeywords = blockedKeywords.slice(-50).reverse();
			keywordList.innerHTML =
				`<h3>屏蔽的关键词 (最新${recentKeywords.length}个):</h3>` +
				recentKeywords.join(", ") +
				(blockedKeywords.length > 50
					? `<div style="color: #666; margin-top: 5px;">...等共${blockedKeywords.length}个关键词</div>`
					: "");
		}

		// 将元素添加到对话框
		dialog.appendChild(titleBar);
		dialog.appendChild(closeButton);
		dialog.appendChild(userList);

		// 添加用户ID输入组到容器
		userIdInputGroup.appendChild(addUserIdInput);
		userIdInputGroup.appendChild(addUserIdButton);

		userInputContainer.appendChild(userIdInputGroup);
		dialog.appendChild(userInputContainer);

		dialog.appendChild(keywordList);

		// 添加关键词输入到容器
		keywordInputGroup.appendChild(addKeywordInput);
		keywordInputGroup.appendChild(addKeywordButton);
		keywordInputContainer.appendChild(keywordInputGroup);
		dialog.appendChild(keywordInputContainer);

		document.body.appendChild(dialog);
	}

	// 隐藏被屏蔽的内容
	function hideBlockedContent() {
		const isListPage = window.location.href.includes("/f?");
		const selectors = {
			thread: {
				container: ".j_thread_list",
				username: ".tb_icon_author, .frs-author-name, img[username]",
				content: ".threadlist_title a, .threadlist_abs",
			},
			post: {
				container: ".l_post_bright",
				username: ".d_name a, .p_author_name, img[username]",
				content: ".d_post_content",
			},
			comment: {
				container: ".lzl_single_post",
				username: ".lzl_cnt .j_user_card, img[username]",
				content: ".lzl_content",
			},
		};

		// 获取完整的用户名(username)
		function getFullUsername(element) {
			// 检查是否有title属性包含"主题作者:"
			const titleElement = element.querySelector(
				'.tb_icon_author[title^="主题作者:"]'
			);
			if (titleElement) {
				const titleText = titleElement.getAttribute("title");
				// 从title中提取完整用户名
				const match = titleText.match(/主题作者:\s*(.*)/);
				if (match && match[1]) {
					return match[1].trim();
				}
			}

			// 检查是否有data-field属性包含un(username)
			const userCardElement = element.querySelector(".j_user_card[data-field]");
			if (userCardElement) {
				try {
					const dataField = JSON.parse(
						userCardElement.getAttribute("data-field")
					);
					if (dataField && dataField.un) {
						return dataField.un;
					}
				} catch (e) {
					// 解析JSON失败时继续检查其他方法
				}
			}

			return null;
		}

		// 在 hideBlockedContent 函数中修改关键词检查部分
		Object.entries(selectors).forEach(([, { container, username, content }]) => {
			const containers = document.querySelectorAll(container);

			containers.forEach((elem) => {
				let shouldHide = false;

				// 使用getUserNames函数获取完整的用户信息
				const authorElems = elem.querySelectorAll(username);

				// 检查每个作者元素
				if (authorElems.length > 0) {
					authorElems.forEach((authorElem) => {
						if (shouldHide) return; // 如果已经决定隐藏，跳过后续检查

						// 使用getUserNames获取完整用户信息
						const userNames = getUserNames(authorElem);

						// 使用新的屏蔽检查逻辑
						if (isUserBlocked(userNames.user_id, userNames.showname)) {
							shouldHide = true;
						}

						// 如果新的检查方法没有匹配，回退到旧的方法
						if (!shouldHide) {
							const authorName = authorElem.getAttribute("username") || authorElem.textContent.trim();
							if (authorName) {
								const matchedUser = blockedUsers.find((user) => {
									return authorName === user || (user.length > 2 && authorName.includes(user));
								});
								if (matchedUser) {
									shouldHide = true;
								}
							}
						}
					});
				}

				// 如果没有找到作者元素，尝试使用旧的getFullUsername方法
				if (!shouldHide && authorElems.length === 0) {
					const fullUsername = getFullUsername(elem);

					if (fullUsername) {
						const matchedUser = blockedUsers.find((user) => {
							return fullUsername === user || (user.length > 2 && fullUsername.includes(user));
						});
						if (matchedUser) {
							shouldHide = true;
						}
					}
				}

				// 检查内容关键词（修改为不区分大小写）
				if (!shouldHide && isListPage) {
					const contentElem = elem.querySelector(content);
					if (contentElem) {
						const contentText = contentElem.textContent.trim().toLowerCase();

						const matchedKeyword = blockedKeywords.find((keyword) =>
							contentText.includes(keyword.toLowerCase())
						);
						if (matchedKeyword) {
							shouldHide = true;
						}
					}
				}

				if (shouldHide) {
					elem.style.display = "none";
				} else {
					// 确保未被屏蔽的内容显示
					if (elem.style.display === "none") {
						elem.style.display = "";
					}
				}
			});
		});
	}





	// 获取用户的所有名称信息
	function getUserNames(element) {
		const userNames = {
			showname: '',
			user_id: ''  // 只保留显示名和用户ID
		};

		try {
			// 安全地获取父容器
			const container = element.closest('.l_post_bright, .j_thread_list, .lzl_single_post, .threadlist_bright');

			if (!container) {
				// 如果没有找到容器，直接从当前元素获取文本
				userNames.showname = element.textContent?.trim() || '';
				return userNames;
			}

			// 获取显示名称 (showname) - 优先从多个来源获取
			const displayNameSelectors = [
				'.d_name a',
				'.frs-author-name',
				'.lzl_cnt .j_user_card',
				'.p_author_name',
				'.tb_icon_author'
			];

			for (const selector of displayNameSelectors) {
				if (userNames.showname) break;

				const displayNameElement = container.querySelector(selector);
				if (displayNameElement) {
					let displayName = '';

					// 特殊处理title属性
					if (selector === '.tb_icon_author') {
						const titleText = displayNameElement.getAttribute('title');
						if (titleText) {
							const match = titleText.match(/主题作者:\s*(.*)/);
							if (match && match[1]) {
								displayName = match[1].trim();
							}
						}
					} else {
						displayName = displayNameElement.textContent?.trim() || '';
					}

					if (displayName) {
						userNames.showname = displayName;
						break;
					}
				}
			}

			// 如果当前元素就是用户名元素，也获取其文本
			if (!userNames.showname && element.textContent) {
				userNames.showname = element.textContent.trim();
			}

			// 提取用户ID - 从多个来源尝试，统一格式处理
			const extractUserIdFromLink = (link) => {
				if (!link || !link.href) return null;

				// 匹配 id= 参数
				const hrefMatch = link.href.match(/id=([^&]+)/);
				if (!hrefMatch) return null;

				let userId = decodeURIComponent(hrefMatch[1]);

				// 处理用户ID格式，去除时间戳参数
				// 格式1: tb.1.12f2a7da.4Hvw8QL4mDTKtz7Nwo5zdw?t=1390369038
				// 格式2: tb.1.a51cbeeb.v7lrS0EbIszf6lJnXUeYqw
				// 统一为格式2，去除 ?t= 及后面的时间戳
				if (userId.includes('?t=')) {
					userId = userId.split('?t=')[0];
				}

				// 验证用户ID格式是否正确 (tb.数字.字符串.字符串)
				const userIdPattern = /^tb\.\d+\.[a-f0-9]+\.[a-zA-Z0-9_-]+$/;
				if (userIdPattern.test(userId)) {
					return userId;
				}

				return null;
			};

			// 1. 从当前元素提取
			if (element.tagName === 'A' && element.href) {
				const userId = extractUserIdFromLink(element);
				if (userId) {
					userNames.user_id = userId;
				}
			}

			// 2. 从容器中的用户链接提取
			if (!userNames.user_id && container) {
				const userLinks = container.querySelectorAll('a[href*="id="]');

				for (const link of userLinks) {
					const userId = extractUserIdFromLink(link);
					if (userId) {
						userNames.user_id = userId;
						break;
					}
				}
			}

		} catch (e) {
			// 发生任何错误时，至少尝试获取元素的文本内容
			userNames.showname = element.textContent?.trim() || '';
		}

		return userNames;
	}

	// 创建悬停屏蔽按钮
	function createHoverBlockButton(userNames) {
		const button = document.createElement('button');
		button.className = 'hover-block-button';
		button.textContent = '屏蔽';

		// 创建提示文本
		const namesList = [];
		if (userNames.showname) namesList.push(`显示名: ${userNames.showname}`);
		if (userNames.user_id) namesList.push(`用户ID: ${userNames.user_id.substring(0, 20)}...`);

		button.title = `点击屏蔽此用户\n${namesList.join('\n')}`;

		button.addEventListener('click', (e) => {
			try {
				e.preventDefault();
				e.stopPropagation();

				// 使用新的添加逻辑
				const isNewUser = addUserToBlockList(userNames.user_id, userNames.showname);

				// 保存和刷新
				if (isNewUser) {
					saveBlockedLists();
					hideBlockedContent();
				}

				// 移除按钮
				if (button.parentNode) {
					button.remove();
				}
				if (currentHoverButton === button) {
					currentHoverButton = null;
				}

				// 显示成功提示
				const toast = document.createElement('div');
				toast.style.cssText = `
					position: fixed;
					top: 20px;
					right: 20px;
					background: ${addedCount > 0 ? '#4CAF50' : '#ff9800'};
					color: white;
					padding: 10px 15px;
					border-radius: 4px;
					z-index: 10001;
					font-size: 14px;
					box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
				`;
				toast.textContent = isNewUser ?
					`已屏蔽用户: ${userNames.showname}` :
					'该用户已在屏蔽列表中';

				document.body.appendChild(toast);

				setTimeout(() => {
					try {
						if (toast.parentNode) {
							toast.parentNode.removeChild(toast);
						}
					} catch (err) {
						// 忽略移除错误
					}
				}, 2000);

			} catch (err) {
				// 出错时至少移除按钮
				try {
					if (button.parentNode) {
						button.remove();
					}
					if (currentHoverButton === button) {
						currentHoverButton = null;
					}
				} catch (removeErr) {
					// 忽略移除错误
				}
			}
		});

		return button;
	}

	// 全局变量存储当前悬停按钮
	let currentHoverButton = null;
	let hoverListenersAdded = false;

	// 添加悬停事件监听
	function addHoverBlockListeners() {
		// 避免重复添加监听器
		if (hoverListenersAdded) {
			return;
		}
		hoverListenersAdded = true;

		// 用户名选择器
		const userNameSelectors = [
			'.d_name a',           // 帖子作者名
			'.frs-author-name',    // 主题列表作者名
			'.lzl_cnt .j_user_card', // 楼中楼用户名
			'.p_author_name',      // 回复作者名
			'.tb_icon_author',     // 主题作者图标
			'.threadlist_author a' // 主题列表作者链接
		];

		// 使用事件委托，只添加一次监听器
		document.addEventListener('mouseenter', (e) => {
			try {
				// 检查目标元素是否存在且有效
				if (!e.target || !e.target.matches) {
					return;
				}

				// 检查是否匹配任何用户名选择器
				const matchedSelector = userNameSelectors.find(selector => {
					try {
						return e.target.matches(selector);
					} catch (err) {
						return false;
					}
				});

				if (matchedSelector) {
					// 移除之前的按钮
					if (currentHoverButton) {
						try {
							currentHoverButton.remove();
						} catch (err) {
							// 忽略移除错误
						}
						currentHoverButton = null;
					}

					const userNames = getUserNames(e.target);

					// 检查是否有有效的用户名信息
					if (!userNames.showname && !userNames.user_id) {
						return;
					}

					const button = createHoverBlockButton(userNames);

					// 定位按钮
					try {
						const rect = e.target.getBoundingClientRect();

						if (rect.width > 0 && rect.height > 0) {
							const leftPos = rect.left;
							const topPos = rect.bottom + window.scrollY + 2;

							button.style.left = leftPos + 'px';
							button.style.top = topPos + 'px';

							document.body.appendChild(button);
							currentHoverButton = button;

							// 鼠标离开用户名区域时移除按钮
							const removeButton = () => {
								setTimeout(() => {
									if (currentHoverButton && currentHoverButton.parentNode) {
										try {
											if (!currentHoverButton.matches(':hover')) {
												currentHoverButton.remove();
												currentHoverButton = null;
											}
										} catch (err) {
											// 如果检查悬停状态失败，直接移除
											currentHoverButton.remove();
											currentHoverButton = null;
										}
									}
								}, 100);
							};

							e.target.addEventListener('mouseleave', removeButton, { once: true });
						}
					} catch (err) {
						// 定位失败时清理按钮
						if (button.parentNode) {
							button.remove();
						}
					}
				}
			} catch (err) {
				// 静默处理所有错误
			}
		}, true);

		// 点击其他地方时移除按钮
		document.addEventListener('click', (e) => {
			if (currentHoverButton && currentHoverButton.parentNode) {
				try {
					if (!currentHoverButton.contains(e.target)) {
						currentHoverButton.remove();
						currentHoverButton = null;
					}
				} catch (err) {
					// 出错时直接移除
					try {
						currentHoverButton.remove();
					} catch (removeErr) {
						// 忽略移除错误
					}
					currentHoverButton = null;
				}
			}
		});
	}

	// 初始化
	function init() {
		addBlockListButton();
		hideBlockedContent();
		addHoverBlockListeners();

		// 监听页面变化
		const observer = new MutationObserver(() => {
			hideBlockedContent();
		});
		observer.observe(document.body, {
			childList: true,
			subtree: true,
		});
	}

	// 页面加载完成后初始化
	if (document.readyState === "loading") {
		document.addEventListener("DOMContentLoaded", init);
	} else {
		init();
	}
})();
