// ==UserScript==
// @name         Bilibili
// @version      1.0
// <AUTHOR>
// @match        *://*.bilibili.com/*
// @grant        GM_getValue
// @grant        GM_setValue
// @grant        GM_addStyle
// @run-at       document-start
// ==/UserScript==

(function () {
  "use strict";
  // 自定义样式定义
  const customStyles = `
:root {
  --background: rgba(255, 255, 255, 0.8);
  --border: 1px solid rgba(255, 255, 255, 0.5);
  --graph_bg_thick: rgba(0, 0, 0, 0.1);
  --border-radius: 16px;
  --bg1: rgba(255, 255, 255, 0);
  --bili-comments-font-size-content: 15px !important;
}

h1,
h2,
h3,
h4,
h5,
h6,
p,
blockquote,
pre,
a,
abbr,
acronym,
address,
cite,
code,
del,
dfn,
em,
img,
q,
s,
samp,
small,
strike,
strong,
sub,
sup,
tt,
var,
dd,
dl,
dt,
li,
ol,
ul,
fieldset,
form,
label,
legend,
button,
table,
caption,
tbody,
tfoot,
thead,
tr,
th,
td {
  font-weight: 500 !important;
}

#action button {
  font-weight: 500 !important;
}

body {
  background: rgba(150, 180, 210, 0.4) !important;
}

.shadow-black\/20 {
  --tw-shadow-color: rgb(0 0 0 / 0.2);
  --tw-shadow: var(--tw-shadow-colored);
  border-radius: 47px !important;
}

#wrap,
.bili-dyn-search-trendings,
.topic-panel {
  display: none !important;
}

#app .bgc,
#app .bg,
.bili-dyn-home--member {
  background: none !important;
}

main section > div:not(:first-child) {
  margin-top: 15px !important;
}

/*-------播放器----------*/
#bilibili-player-placeholder,
.bpx-player-container {
  box-shadow: none !important;
}

#bilibili-player-placeholder,
.bpx-player-container {
  box-shadow: none !important;
  border-radius: 16px !important;
  overflow: hidden;
}

/*-------动态页----------*/
.bili-dyn-list-notification {
  background: rgba(255, 255, 255, 0.8) !important;
  border-radius: 16px !important;
  color: orangered !important;
}

.bili-dyn-list-tabs__item {
  font-weight: 500 !important;
}

.mini-header {
  background: rgba(0, 0, 0, 0.2) !important;
  backdrop-filter: blur(50px);
}

.bili-dyn-list-tabs {
  border-radius: 16px !important;
  background-color: rgba(255, 255, 255, 0.8) !important;
}

.bili-dyn-item {
  background-color: rgba(255, 255, 255, 0.8) !important;
  margin-bottom: 15px !important;
  border-radius: 16px !important;
  box-shadow: 0 1px 10px rgba(0, 0, 0, 0.03) !important;
}

.bili-header .search-panel {
  border: none !important;
  border-radius: 16px !important;
  margin: 15px 0;
  backdrop-filter: blur(30px) saturate(180%);
  box-shadow: 0 1px 50px rgba(0, 0, 0, 0.2), inset 0px 0px 60px 20px rgba(255, 255, 255, 0.5);
  background: rgba(240, 242, 244, 0.5) !important;
}

.bili-header .header .title {
  font-size: 15px !important;
}

.bili-header .histories .history-item {
  background: rgba(0, 0, 0, 0.05) !important;
  border-radius: 8px !important;
}

.bili-header .histories .history-item .close svg {
  fill: #888;
}

.bili-header .histories .history-item:hover,
.bili-header .histories .history-item:focus {
  color: #333;
}

.mini-header .center-search-container .center-search__bar #nav-searchform.is-focus {
  border: none !important;
  border-bottom: none !important;
  background: rgba(0, 0, 0, 0.15) !important;
}

.bili-header .header .clear {
  color: #333;
}

.bili-header .center-search-container .center-search__bar #nav-searchform {
  line-height: 36px !important;
  border: none !important;
  height: 36px !important;
  border-radius: var(--border-radius) !important;
  opacity: 1 !important;
  background: rgba(0, 0, 0, 0.1) !important;
}

.bili-header .center-search-container .center-search__bar .nav-search-content .nav-search-input:focus {
  background: none !important;
}

.bili-header .center-search-container .center-search__bar #nav-searchform.is-actived .nav-search-content, .bili-header .center-search-container .center-search__bar #nav-searchform.is-focus .nav-search-content {
	background-color: transparent !important;
}

.bili-header .center-search-container .center-search__bar.is-focus {
  box-shadow: none !important;
}

.bili-header .center-search-container {
  height: 34px !important;
}

.bili-header .center-search-container .center-search__bar #nav-searchform.is-actived .nav-search-content,
.bili-header .center-search-container .center-search__bar #nav-searchform.is-focus .nav-search-content {
  background-color: transparent;
}

.v-popover-content {
  background: rgba(255, 255, 255, 0.8) !important;
  box-shadow: 0 0 16px rgba(0, 0, 0, 0.2) !important;
  border: none !important;
  backdrop-filter: blur(20px);
  border-radius: 16px !important;
}

.bili-header .avatar-panel-popover {
  background: transparent !important;
}

.v-popover {
  transition: 0.1s !important;
}

.bili-header .message-entry-popover .message-inner-list__item {
  padding: 10px !important;
  color: #333 !important;
  font-size: 14px !important;
  transition: background-color 0.1s !important;
  border-radius: 8px !important;
  margin: 0 10px !important;
}

.bili-header .message-entry-popover .message-inner-list__item:hover {
  background-color: rgba(0, 0, 0, 0.1) !important;
}

.v-popover.is-right {
  background: rgba(255, 255, 255, 0.7) !important;
}

.bili-user-profile {
  box-shadow: 0 0 30px 2px rgba(0, 0, 0, 0.2) !important;
}

.b-avatar__layer.center {
  box-shadow: 0 1px 10px rgba(0, 0, 0, 0.25);
  border: 3px solid #000;
  border-radius: 50% !important;
  width: 48px !important;
  height: 48px !important;
}

.dynamic-all .history-tip[data-v-3a378ba6] {
  display: none !important;
}

.dynamic-all .split-line[data-v-3a378ba6]:before {
  border-top: none !important;
}

.bili-header .header-dynamic-list-item[data-v-1e1051ac]:hover {
  background: rgba(0, 0, 0, 0.1) !important;
}

.bili-header .header-dynamic-list-item[data-v-1e1051ac] {
  margin: 0 10px;
  border-radius: 8px !important;
}

.bili-header .header-history-video:hover {
  background: rgba(0, 0, 0, 0.1) !important;
}

.bili-header .header-history-video {
  margin: 0 10px !important;
  border-radius: 8px !important;
}

.history-panel-popover .header-tabs-panel__item--active {
  border-bottom: none !important;
}

.history-panel-popover .header-tabs-panel {
  border-bottom: none !important;
}

.history-panel-popover .header-history-card__info--date,
.history-panel-popover .header-history-card__info--name {
  color: #515863 !important;
}

.bili-cascader-options {
  border: none !important;
  border-radius: 8px !important;
  box-shadow: 0 0 16px rgba(0, 0, 0, 0.1) !important;
  max-height: 300px !important;
  min-width: 86px !important;
  padding: 12px 4px !important;
  background: rgba(255, 255, 255, 1) !important;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.bili-cascader-options__item:not(.is-disabled):hover {
  background-color: #f1f2f3;
  border-radius: 8px;
}

.bili-cascader-options__item {
  color: #333 !important;
}

.bili-header .bili-header-channel-panel .channel-panel__column {
  border-right: none !important;
}

.v-popover.is-bottom {
  top: 105% !important;
}

::-webkit-scrollbar {
  display: none !important;
}

.dynamic-panel-popover .header-tabs-panel__content {
  max-height: 670px !important;
}

.history-panel-popover .header-tabs-panel__content {
  height: 640px !important;
}

.history-panel-popover {
  height: 700px !important;
}

.bili-header .header-history-video__image .v-img img {
  border-radius: 6px !important;
}

.bili-header .header-history-video__progress {
  display: none !important;
}

.v-img {
  background-color: transparent !important;
}

.history-panel-popover .header-history-card__info--date,
.history-panel-popover .header-history-card__info--name {
  font-size: 11px !important;
  line-height: 17px !important;
}

#editor {
  border-radius: 12px !important;
  background-color: rgba(0, 0, 0, 0.05) !important;
}

.bili-dyn-live-users {
  border: var(--border) !important;
  border-radius: var(--border-radius) !important;
  background: var(--background) !important;
  box-shadow: 0 1px 10px rgba(0, 0, 0, 0.1), inset 0px 0px 100px 10px rgba(255, 255, 255, 0.5) !important;
}

.bili-dyn-my-info {
  border: var(--border) !important;
  border-radius: var(--border-radius) !important;
  background: var(--background) !important;
  box-shadow: 0 1px 10px rgba(0, 0, 0, 0.1), inset 0px 0px 100px 10px rgba(255, 255, 255, 0.5) !important;
}

.bili-dyn-banner {
  border-radius: 16px !important;
  overflow: hidden !important;
}

/*--------------搜索页-----------------*/
.search-input-wrap .search-panel {
  backdrop-filter: blur(30px) saturate(180%);
  border: none;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.8) !important;
}

.search-input-container .search-input-wrap[data-v-64f8f917]:hover,
.search-input-container .search-input-wrap.search-input-focus[data-v-64f8f917] {
  background: rgba(255, 255, 255, 0.8);
}

.search-input-wrap .suggest-item {
  border-radius: 8px;
}

.search-input-wrap .suggest-item.active,
.search-input-wrap .suggest-item:hover,
.search-input-wrap .suggest-item:focus {
  background: rgba(0, 0, 0, 0.1);
}

.search-input-container .search-input-wrap {
  border-radius: var(--border-radius) !important;
}

.search-input-container .search-input-wrap .search-panel-popover[data-v-64f8f917] {
  padding: 16px 8px !important;
}

.bili-video-card .bili-video-card__info--tit[data-v-98b7e558]:hover {
  color: none !important;
}

.bili-video-card {
  border-radius: 12px !important;
  padding: 0 0 10px 0 !important;
  transition: box-shadow 0.35s cubic-bezier(0.4, 2, 0.6, 1), transform 0.35s cubic-bezier(0.4, 2, 0.6, 1), background 0.35s, border 0.35s;
  background: rgba(255, 255, 255, 0.65) !important;
  border: 1px solid rgba(255, 255, 255, 0.3) !important;
}

.bili-video-card__wrap {
  background: transparent !important;
  padding: 10px;
}

.bili-video-card__wrap[data-v-0de3a6aa] {
  padding: 0;
}

.col_3 {
  flex: 0 0 300px !important;
  max-width: 30% !important;
  min-width: 325px !important;
}

.recommended-container_floor-aside .container {
  display: grid !important;
  grid-template-columns: repeat(auto-fit, minmax(380px, 1fr)) !important;
  grid-gap: 40px 20px !important;
}

.bili-video-card__wrap[data-v-4a0a9b1c] {
  background-color: transparent !important;
}

.large-header {
  background-color: transparent !important;
}

.bili-header .bili-header__bar {
  background-color: rgba(255, 255, 255, 0.8) !important;
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255) !important;
  width: 99% !important;
  margin: 10px !important;
  border-radius: 16px !important;
}

.bili-video-card__wrap[data-v-4a0a9b1c] {
  padding: 5px !important;
}

.bili-video-card .bili-video-card__image[data-v-603f41ad] {
  border-radius: 12px;
}

.bili-video-card__wrap[data-v-603f41ad] {
  padding: 0px 5px;
}

.bili-video-card:hover {
  transform: scale(1.05);
  box-shadow: 0 1px 30px rgba(0, 0, 0, 0.05) !important;
  will-change: transform, box-shadow, border;
  transition: box-shadow 0.35s cubic-bezier(0.4, 2, 0.6, 1), transform 0.35s cubic-bezier(0.4, 2, 0.6, 1), background 0.35s, border 0.35s;
}

.bili-video-card .bili-video-card__info--tit[data-v-603f41ad]:hover {
  color: #000;
}

.search-input-wrap .histories .history-item {
  background: rgba(0, 0, 0, 0.07) !important;
  border-radius: 10px !important;
}

.bili-video-card .bili-video-card__info--author {
  font-weight: 600 !important;
}

/*-------首页----------*/
#i_cecream {
  margin: 0 auto;
  max-width: 2560px;
  background-color: transparent !important;
}

.video-page-card-small {
  margin-bottom: 20px !important;
  background: rgba(255, 255, 255, 0.8);
  padding: 5px;
  border-radius: 12px !important;
  border: 1px solid rgb(255, 255, 255) !important;
}

.video-page-card-small .card-box .info .title {
  font-size: 14px !important;
}

.video-page-card-small .card-box .info .upname {
  font-size: 13px !important;
}

.video-page-card-small .card-box .info .playinfo {
  font-size: 12px !important;
}

.text-\[15px\] {
  font-size: 12px;
}

.bili-video-card .bili-video-card__cover {
  box-shadow: 0 1px 15px rgba(0, 0, 0, 0.15);
}

.bili-video-card__cover img {
  border-radius: 10px !important;
}

.bili-video-card .bili-video-card__info--icon-text {
  color: #fff !important;
  background-color: #979ca2 !important;
  border-radius: 6px !important;
  margin-right: 4px;
  font-size: 11px !important;
  line-height: 15px !important;
  height: 19px !important;
  padding: 2px 8px !important;
}

.bili-video-card .bili-video-card__info--owner {
  color: #565d69;
  font-size: 13px;
}

.bili-video-card a {
  font-size: 14px;
}

.bili-video-card a:not(.disable-hover):hover {
  color: #18191c;
}

.bili-video-card .bili-video-card__image {
  border-radius: 12px;
  overflow: hidden;
}

#editor {
  width: 100%;
  padding: 8px 0px;
  border: 1px solid var(--Ga1);
  box-sizing: border-box;
  border-radius: 6px !important;
  background-color: var(--bg3) !important;
  transition: 0.2s;
  cursor: text;
  border-radius: 12px !important;
  background-color: rgba(0, 0, 0, 0.05) !important;
}

.bui-collapse .bui-collapse-header {
  background: var(--background) !important;
}

.bpx-player-sending-bar .bpx-player-video-inputbar {
  background: rgba(0, 0, 0, 0.1) !important;
}

.bpx-player-sending-bar {
  background: #fff !important;
}

.video-container-v1[data-v-6b2dd639] {
  background: #fff !important;
}

#dialog {
  background-color: rgba(255, 255, 255, 0.8) !important;
  border-radius: 16px !important;
  font-weight: 500 !important;
  box-shadow: 0 1px 50px rgba(0, 0, 0, 0.2) !important;
  backdrop-filter: blur(20px) saturate(180%) !important;
}

.search-layout .search-header .search-line[data-v-22ff42d3] {
  border-top: none;
}

.bili-feed4-layout,
.bili-feed4 .bili-header .bili-header__channel {
  padding: 0 40px !important;
}

.video-container-v1[data-v-6b2dd639] {
  background: transparent !important;
}

.video-tag-container .tag-panel .tag-link {
  color: #fff !important;
  background: rgba(0, 0, 0, 0.2) !important;
  height: 22px !important;
  line-height: 20px !important;
  border-radius: 10px !important;
  font-size: 12px !important;
}

.video-pod[data-v-dac4fbd2] {
  border-radius: 12px !important;
  background: rgba(255, 255, 255, 0.8) !important;
}

.simple-base-item .title .title-txt {
  font-size: 14px !important;
}

.video-pod .video-pod__header .header-bottom .right .subscribe-btn[data-v-dac4fbd2] {
  font-size: 12px !important;
  width: 65px !important;
  height: 22px !important;
  border-radius: 6px !important;
}

#danmukuBox {
  border-radius: 12px !important;
  overflow: hidden !important;
}

.upinfo-btn-panel .follow-btn.following[data-v-3ff36384] {
  color: #fff !important;
  background-color: #000 !important;
}

.video-page-card-small .card-box .pic-box {
  border-radius: 10px !important;
  overflow: hidden !important;
}

.search-input-container .search-input-wrap[data-v-64f8f917] {
  position: relative;
  width: var(--search_input_width);
  height: var(--search_input_height);
  max-height: var(--search_input_max_h);
  margin: 0 auto;
  padding: 5px;
  background: var(--bg2);
  border: none !important;
  border-radius: 6px;
  transition: all 0.2s;
}

.search-input-container .search-input-wrap .search-button[data-v-64f8f917] {
  border-radius: 12px !important;
}

.video-tag-container .tag-panel .tag-link .tag-icon {
  width: 12px !important;
  height: 12px !important;
}

#commentapp {
  background: rgba(255, 255, 255, 0.7) !important;
  border-radius: 16px !important;
  border: 1px solid rgb(255, 255, 255) !important;
  padding: 20px;
}

.video-tag-container[data-v-cf3f07d2] {
  border-bottom: none !important;
}

.video-toolbar-container[data-v-52e430cf] {
  border-bottom: none !important;
}

.message-bg {
  background-image: none !important;
}

.message-bgc {
  background-color: transparent !important;
}

#app {
  margin-top: 25px !important;
}

.message-layout,
.m-infinite-scroll {
  border-radius: 16px !important;
  overflow: hidden;
}

.message-content {
  padding: 10px !important;
}

.message-header {
  border-radius: 12px !important;
  overflow: hidden;
  box-shadow: none !important;
}

.message-box-shadow {
  border-radius: 12px !important;
  background: var(--bg1);
  box-shadow: none !important;
}

.interaction-item {
  background: rgba(255, 255, 255, 0.8);
  margin: 10px;
  border-radius: 16px !important;
}

.message-box-shadow {
  background: transparent !important;
}

.interaction-item__right {
  border-bottom: none !important;
}

.bili-dyn-list__notification {
  margin-bottom: 10px;
}

.user-list .card-bottom[data-v-80d1cac0] {
  border-bottom: none !important;
}

.bili-feed4-layout,
.bili-feed4 .bili-header .bili-header__channel {
  margin: 20px auto !important;
}

#editor {
  border-radius: 12px !important;
}

  `;
  const STYLE_ID = "bilibili-enhanced-styles";
  // 样式注入函数
  function injectStyles() {
    // 避免重复注入
    if (document.getElementById(STYLE_ID)) return;

    // 优先使用 GM_addStyle
    if (typeof GM_addStyle === "function") {
      try {
        GM_addStyle(customStyles);
        return;
      } catch { } // 静默失败，转用备选方案
    }

    // 备选方案：创建 style 元素
    const styleEl = document.createElement("style");
    styleEl.id = STYLE_ID;
    styleEl.textContent = customStyles;

    // 优化注入时机
    if (document.head) {
      requestAnimationFrame(() => document.head.appendChild(styleEl));
    } else {
      // 仅在必要时使用 MutationObserver
      const observer = new MutationObserver((_, obs) => {
        if (document.head) {
          requestAnimationFrame(() => document.head.appendChild(styleEl));
          obs.disconnect();
        }
      });
      observer.observe(document.documentElement, { childList: true });
    }
  }


  // 递归搜索 Shadow DOM 的函数
  function searchShadowDOM(root, depth = 0) {
    let shadowCount = 0;
    let optionsCount = 0;

    // 搜索当前层级的所有元素
    const elements = root.querySelectorAll('*');

    elements.forEach(element => {
      if (element.shadowRoot) {
        shadowCount++;

        // 检查 Shadow DOM 中是否有 #options 元素
        const optionsElement = element.shadowRoot.querySelector('#options');
        if (optionsElement) {
          optionsCount++;

          // 检查是否已经注入过样式
          const existingStyle = element.shadowRoot.querySelector('#custom-options-style');
          if (!existingStyle) {
            const shadowStyle = document.createElement('style');
            shadowStyle.id = 'custom-options-style';
            shadowStyle.textContent = `
              #options {
                padding: 5px !important;
                border-radius: 10px !important;
                background-color: rgba(255, 255, 255, 0.6) !important;
                box-shadow: rgba(0, 0, 0, 0.1) 0px 1px 20px !important;
                backdrop-filter: blur(20px) saturate(180%) !important;
              }
              #options li {
                border-radius: 10px !important;
              }
              #options li:hover {
                background-color: rgba(0, 0, 0, 0.05) !important;
              }
            `;
            element.shadowRoot.appendChild(shadowStyle);
          }
        }

        // 处理 #dialog 元素
        const dialogElement = element.shadowRoot.querySelector('#dialog');
        if (dialogElement) {
          const existingDialogStyle = element.shadowRoot.querySelector('#custom-dialog-style');
          if (!existingDialogStyle) {
            const shadowStyle = document.createElement('style');
            shadowStyle.id = 'custom-dialog-style';
            shadowStyle.textContent = `
              #dialog {
                background: white !important;
                font-weight: 500 !important;
                opacity: 1 !important;
                z-index: 9999 !important;
              }
            `;
            element.shadowRoot.appendChild(shadowStyle);
          }
        }

        // 递归搜索嵌套的 Shadow DOM
        if (depth < 5) { // 限制递归深度避免无限循环
          const nested = searchShadowDOM(element.shadowRoot, depth + 1);
          shadowCount += nested.shadowCount;
          optionsCount += nested.optionsCount;
        }
      }
    });

    return { shadowCount, optionsCount };
  }

  // 修改 Shadow DOM 样式的函数
  function modifyShadowDOM() {
    searchShadowDOM(document);
  }

  // 持续监听 Shadow DOM 元素的出现
  function startShadowObserver() {
    // 立即尝试一次
    modifyShadowDOM();

    // 设置定时器持续检查（更频繁）
    setInterval(modifyShadowDOM, 200);

    // 使用 MutationObserver 监听 DOM 变化
    const observer = new MutationObserver((mutations) => {
      let shouldUpdate = false;
      mutations.forEach((mutation) => {
        // 监听所有节点变化，因为 Shadow DOM 可能在任何元素中
        if (mutation.addedNodes.length > 0) {
          shouldUpdate = true;
        }
      });

      if (shouldUpdate) {
        setTimeout(modifyShadowDOM, 10);
      }
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true
    });

    // 额外监听点击事件，因为菜单可能是点击时才显示
    document.addEventListener('click', () => {
      setTimeout(modifyShadowDOM, 100);
    });
  }

  // 在 DOMContentLoaded 之前注入
  if (document.readyState === "loading") {
    document.addEventListener("DOMContentLoaded", () => {
      injectStyles();
      startShadowObserver();
    }, { once: true });
  } else {
    injectStyles();
    startShadowObserver();
  }
})();
