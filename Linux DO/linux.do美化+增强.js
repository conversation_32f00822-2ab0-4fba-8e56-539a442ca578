// ==UserScript==
// @name         linux.do美化+增强
// @namespace    http://tampermonkey.net/
// @version      0.5
// @description  回复重构功能 + 美化布局功能 + 屏蔽功能
// <AUTHOR>
// @match        https://linux.do/*
// @grant        GM_addStyle
// @grant        GM_getValue
// @grant        GM_setValue
// @run-at       document-start
// ==/UserScript==

(function() {
    'use strict';

    // ========== 增强的网站原生加载检测系统 ==========
    let isNativeLoading = false;
    let nativeLoadingTimer = null;
    let originalFetch = null;
    let activeRequests = 0;
    let lastScrollPosition = 0;
    let isScrollingDown = false;

    // 增强的网站原生加载检测
    function detectNativeLoading() {
        // 检测更全面的加载指示器
        const loadingIndicators = [
            '.loading',
            '.spinner',
            '[data-loading]',
            '.discourse-loading',
            '.loading-container',
            '.loading-spinner',
            '.topic-list-loading',
            '.ember-loading',
            '.loading-posts',
            '[aria-busy="true"]',
            '.discourse-loading-indicator'
        ];

        for (const selector of loadingIndicators) {
            if (document.querySelector(selector)) {
                return true;
            }
        }

        // 检测滚动方向（不再以接近底部作为加载判据）
        const currentScroll = window.scrollY;
        isScrollingDown = currentScroll > lastScrollPosition;
        lastScrollPosition = currentScroll;

        return false;
    }

    // 初始化网络请求监听（只执行一次）
    function initNetworkMonitoring() {
        if (originalFetch || !window.fetch) return;

        originalFetch = window.fetch;
        window.fetch = function(...args) {
            // 检查是否是关键的论坛请求（排除头像等静态资源）
            const url = args[0];
            const isKeyForumRequest = typeof url === 'string' && (
                url.includes('/posts.json') ||
                url.includes('/t/') && url.includes('.json') ||
                url.includes('/raw/') ||
                url.includes('/timings') ||
                url.includes('/message-bus/')
            );

            // 排除头像和静态资源请求
            const isStaticResource = typeof url === 'string' && (
                url.includes('/user_avatar/') ||
                url.includes('/letter_avatar/') ||
                url.includes('/uploads/') ||
                url.includes('/images/emoji/') ||
                url.includes('.png') ||
                url.includes('.jpg') ||
                url.includes('.gif') ||
                url.includes('.svg')
            );

            if (isKeyForumRequest && !isStaticResource) {
                activeRequests++;
                isNativeLoading = true;
            }

            const promise = originalFetch.apply(this, args);

            if (isKeyForumRequest && !isStaticResource) {
                promise.finally(() => {
                    activeRequests--;
                    if (activeRequests === 0) {
                        clearTimeout(nativeLoadingTimer);
                        nativeLoadingTimer = setTimeout(() => {
                            isNativeLoading = false;
                        }, 1500); // 进一步增加延迟
                    }
                });
            }

            return promise;
        };
    }



    // ========== 浏览器检测 ==========
    const isFirefox = navigator.userAgent.toLowerCase().includes('firefox');

    // ========== 配置 - 合并优化 ==========
    const CONFIG = {
        DEBUG: false, // 关闭调试模式
        // 统一的延迟配置
        PROCESS_DELAY: isFirefox ? 30 : 20, // 减少处理延迟
        MAX_POSTS_PER_BATCH: isFirefox ? 8 : 12, // 增加批处理大小
        BATCH_SIZE: isFirefox ? 2 : 3, // 增加批处理大小
        THROTTLE_DELAY: isFirefox ? 200 : 150, // 减少节流延迟
        FETCH_DELAY: isFirefox ? 30 : 20, // 减少获取延迟
        MAX_FETCH_ATTEMPTS: 3, // 增加重试次数
        QUOTE_STYLE_ID: 'optimized-quote-styles',
        BEAUTIFY_STYLE_ID: 'linux-do-beautify-styles',
        CACHE_TTL: 30 * 60 * 1000, // 增加缓存时间到30分钟
        CACHE_CLEAN_INTERVAL: isFirefox ? 180000 : 240000, // 增加缓存清理间隔
        // 新增性能优化配置
        VIEWPORT_BUFFER: 800, // 增加视口缓冲区
        MAX_CONCURRENT_PROCESSING: 10, // 增加最大并发处理数
        IDLE_TIMEOUT: 50, // 减少空闲超时
        LONG_POST_THRESHOLD: 50, // 长帖子阈值
        // 长内容处理配置
        CONTENT_LENGTH_THRESHOLD: 5000, // 长内容阈值（字符数）
        MAX_QUOTE_LENGTH: 1500, // 引用最大显示长度
        MAX_IMAGES_IN_QUOTE: 10, // 引用中最大图片数量
        ULTRA_LONG_CONTENT_THRESHOLD: 10000, // 超长内容阈值
        // 多线程加载配置 - 优化引用加载速度
        MAX_CONCURRENT_FETCHES: 12, // 增加最大并发网络请求数
        FETCH_TIMEOUT: 5000, // 增加单个请求超时时间
        RETRY_DELAY: 50, // 减少重试延迟
        PARALLEL_BATCH_SIZE: 6, // 增加并行批处理大小
        // 恢复核心功能的智能加载配置 - 平衡功能与冲突避免
        SMART_PRELOAD_ENABLED: true, // 重新启用智能预加载
        VIEWPORT_PRELOAD_DISTANCE: 800, // 适中的预加载距离
        SCROLL_LOAD_PROBABILITY: 0.3, // 适中的滚动时加载概率
        NATIVE_LOADING_DELAY: 1000, // 适中的网站原生加载延迟
        AGGRESSIVE_CACHE_ENABLED: true // 重新启用缓存策略
    };

    // ========== 日志系统 ==========
    const log = CONFIG.DEBUG ? (...args) => console.log(`[${isFirefox ? 'Firefox' : 'Chrome'}]`, ...args) : () => {};

    // ========== 全局错误处理 ==========
    async function safeExecuteAsync(fn) {
        try {
            return await fn();
        } catch (error) {
            return null;
        }
    }

    // ========== DOM保护机制已移除 ==========
    // 为了避免与网站原生代码冲突，移除DOM保护机制
    // 改为在我们自己的代码中使用安全的DOM操作



    // ========== 性能监控 ==========
    const performanceStats = {
        quotesProcessed: 0,
        totalProcessingTime: 0,
        averageProcessingTime: 0,
        domOperations: 0,
        cacheHits: 0,
        startTime: performance.now()
    };

    function updatePerformanceStats(processingTime) {
        performanceStats.quotesProcessed++;
        performanceStats.totalProcessingTime += processingTime;
        performanceStats.averageProcessingTime = performanceStats.totalProcessingTime / performanceStats.quotesProcessed;

        if (performanceStats.quotesProcessed % 10 === 0) {
            log(`性能统计: 已处理${performanceStats.quotesProcessed}个引用，平均耗时${performanceStats.averageProcessingTime.toFixed(2)}ms`);
        }
    }

    // ========== 全局状态 ==========
    const postCache = new Map();
    let isProcessing = false;
    let lastProcessTime = 0;
    let rafId = null;
    let processingQueue = [];
    let concurrentProcessing = 0;
    let isLongPost = false;

    // ========== 多线程加载状态管理 ==========
    const fetchPromises = new Map(); // 存储Promise对象
    let activeFetches = 0; // 当前活跃的请求数
    const pendingFetches = []; // 等待处理的请求队列
    const fetchTimeouts = new Map(); // 请求超时管理

    // ========== 优化的样式系统 - 合并重复样式 ==========
    function initStyles() {
        if (document.getElementById(CONFIG.QUOTE_STYLE_ID)) return;

        // 创建其他必要的样式（如果有的话）
        const style = document.createElement('style');
        style.id = CONFIG.QUOTE_STYLE_ID;
        style.textContent = `
            /* 其他必要的样式可以在这里添加 */
        `;

        (document.head || document.documentElement).appendChild(style);
    }



    // ========== 美化样式系统 ==========
    function initBeautifyStyles() {
        // 使用GM_addStyle添加美化样式
        GM_addStyle(`
            /* ========== 楼层号样式管理 ========== */
            /* 为帖子容器设置相对定位 */
            article[id^="post_"] {
                position: relative !important;
            }

            /* 隐藏原始的楼层号显示 */
            article[id^="post_"] .topic-meta-data::after {
                display: none !important;
            }

            /* 楼层号伪元素样式 */
article[id^="post_"][data-floor-number]::after {
	content: attr(data-floor-number) !important;
	position: absolute !important;
	left: 96% !important;
	background: #000 !important;
	color: #fff !important;
	border-radius: 6px !important;
	text-align: center !important;
	display: block !important;
	visibility: visible !important;
	opacity: 0.8 !important;
	pointer-events: none !important;
	top: -5px !important;
	transform: translate(50%, 50%) !important;
	width: 24px !important;
	height: 18px !important;
	font-size: 11px !important;
	line-height: 18px !important;
	box-shadow: 0 1px 6px rgba(0,0,0,0.1) !important;
}

            /* 优化的回复引用样式 */
            .optimized-reply-quote {
                margin-bottom: 10px !important;
                padding: 8px 12px !important;
                background: rgba(0, 0, 0, 0.05) !important;
                border-radius: 10px !important;
                font-size: 14px !important;
                display: block !important;
                opacity: 1 !important;
                position: relative !important;
                z-index: 300 !important;
            }

            /* 引用头部样式 */
            .optimized-reply-quote .quote-header {
                font-weight: bold !important;
                margin: 5px !important;
                color: #555 !important;
                display: flex !important;
                align-items: center !important;
            }

            /* 引用头像样式 */
            .optimized-reply-quote .quote-avatar {
                width: 24px !important;
                height: 24px !important;
                border-radius: 4px !important;
                margin-right: 8px !important;
                border: 2px solid rgba(0, 0, 0) !important;
                flex-shrink: 0 !important;
                overflow: hidden;
                line-height: 24;
            }

            /* 引用作者名样式 */
            .optimized-reply-quote .quote-author {
                font-weight: bold !important;
                color: #555 !important;
            }

            /* 引用内容样式 */
            .optimized-reply-quote .quote-content {
                color: #666 !important;
                line-height: 1.4 !important;
                word-wrap: break-word !important;
                padding: 0 0 0 4px !important;
            }

            /* ========== 隐藏原始元素 ========== */

            /* 隐藏嵌入的帖子 */
            .embedded-posts.top,
            article[id^="post_"]:not([data-quote-processed="true"]) .embedded-posts.top {
                display: none !important;
            }

            /* 隐藏回复标签 */
            .reply-to-tab {
                opacity: 0 !important;
                pointer-events: none !important;
                position: absolute !important;
                z-index: -1 !important;
            }

            /* 禁用头像滚动跟随功能 */
            .topic-post.sticky-avatar .topic-avatar {
                position: relative !important;
                top: unset !important;
            }
:root {
   --primary-very-low: rgba(0, 0, 0, 0.1) !important;
   --header_primary-low-mid: rgba(0, 0, 0, 0.5) !important;
   --primary-medium: rgba(0, 0, 0, 0.8) !important;
}

body::before, body::after {
    content: "";
    display: none !important;
}

.custom-footer,
.top-menu,
.sidebar-footer-wrapper .sidebar-footer-container::before,
.names .user-title,
.user-menu.revamped #quick-access-profile hr,
.topic-post-badges,
.desktop-view nav.post-controls .show-replies,
.small-action.topic-post-visited .topic-post-visited-line,
.timeline-container,
#list-area .show-more,
.house-creative,
.topic-list tr.selected td:first-of-type, .topic-list-item.selected td:first-of-type, .latest-topic-list-item.selected, .search-results .fps-result.selected {
	box-shadow: none !important;
  display: none !important;
}

a {
  color: rgba(0, 0, 0, 0.8) !important;
	text-decoration: none !important;
}

a:hover {
	color: #bb4e5b !important;
}

.user-menu .quick-access-panel li a > div, .user-menu .quick-access-panel li button > div, .user-notifications-list li a > div, .user-notifications-list li button > div {
	font-size: 14px !important;
}
.user-menu.show-avatars li.notification a .icon-avatar__icon-wrapper, .user-menu.show-avatars li.bookmark a .icon-avatar__icon-wrapper, .user-menu.show-avatars li.reviewable a .icon-avatar__icon-wrapper, .user-menu.show-avatars li.message a .icon-avatar__icon-wrapper, .user-notifications-list.show-avatars li.notification a .icon-avatar__icon-wrapper, .user-notifications-list.show-avatars li.bookmark a .icon-avatar__icon-wrapper, .user-notifications-list.show-avatars li.reviewable a .icon-avatar__icon-wrapper, .user-notifications-list.show-avatars li.message a .icon-avatar__icon-wrapper {
	width: 14px !important;
	height: 14px !important;
	font-size: 10px !important;
	box-shadow: 0 0 10px rgba(0, 0, 0, 0.3) !important;
}
.user-menu.show-avatars li.notification a .icon-avatar .avatar, .user-menu.show-avatars li.bookmark a .icon-avatar .avatar, .user-menu.show-avatars li.reviewable a .icon-avatar .avatar, .user-menu.show-avatars li.message a .icon-avatar .avatar, .user-notifications-list.show-avatars li.notification a .icon-avatar .avatar, .user-notifications-list.show-avatars li.bookmark a .icon-avatar .avatar, .user-notifications-list.show-avatars li.reviewable a .icon-avatar .avatar, .user-notifications-list.show-avatars li.message a .icon-avatar .avatar {
	border-radius: 8px !important;
	box-shadow: 0 0 6px rgba(0, 0, 0, 0.2) !important;
	border: 2px solid rgba(0, 0, 0) !important;
}
.user-menu.revamped .tabs-list .btn .d-icon, .user-menu.revamped .tabs-list .d-modal.json-schema-editor-modal .je-ready .json-editor-btn-add .d-icon, .d-modal.json-schema-editor-modal .je-ready .user-menu.revamped .tabs-list .json-editor-btn-add .d-icon, .user-menu.revamped .tabs-list .d-modal.json-schema-editor-modal .je-ready button .d-icon, .d-modal.json-schema-editor-modal .je-ready .user-menu.revamped .tabs-list button .d-icon {
	color: rgba(0, 0, 0, 0.8) !important;
}
.discourse-no-touch .user-menu.revamped .tabs-list .btn:hover, .discourse-no-touch .user-menu.revamped .tabs-list .d-modal.json-schema-editor-modal .je-ready .json-editor-btn-add:hover, .d-modal.json-schema-editor-modal .je-ready .discourse-no-touch .user-menu.revamped .tabs-list .json-editor-btn-add:hover, .discourse-no-touch .user-menu.revamped .tabs-list .d-modal.json-schema-editor-modal .je-ready button:hover, .d-modal.json-schema-editor-modal .je-ready .discourse-no-touch .user-menu.revamped .tabs-list button:hover {
	background-color: transparent !important;
  color: rgba(0, 0, 0, 0.4) !important;
}
.user-menu.revamped .tabs-list .btn.active, .user-menu.revamped .tabs-list .d-modal.json-schema-editor-modal .je-ready .active.json-editor-btn-add, .d-modal.json-schema-editor-modal .je-ready .user-menu.revamped .tabs-list .active.json-editor-btn-add, .user-menu.revamped .tabs-list .d-modal.json-schema-editor-modal .je-ready button.active, .d-modal.json-schema-editor-modal .je-ready .user-menu.revamped .tabs-list button.active {
	background-color: transparent !important;
}
.menu-panel .panel-body-bottom .btn, .menu-panel .panel-body-bottom .d-modal.json-schema-editor-modal .je-ready .json-editor-btn-add, .d-modal.json-schema-editor-modal .je-ready .menu-panel .panel-body-bottom .json-editor-btn-add, .menu-panel .panel-body-bottom .d-modal.json-schema-editor-modal .je-ready button, .d-modal.json-schema-editor-modal .je-ready .menu-panel .panel-body-bottom button {
	background-color: transparent !important;
}
.menu-panel .panel-body-bottom .btn:hover, .menu-panel .panel-body-bottom .d-modal.json-schema-editor-modal .je-ready .json-editor-btn-add:hover, .d-modal.json-schema-editor-modal .je-ready .menu-panel .panel-body-bottom .json-editor-btn-add:hover, .menu-panel .panel-body-bottom .d-modal.json-schema-editor-modal .je-ready button:hover, .d-modal.json-schema-editor-modal .je-ready .menu-panel .panel-body-bottom button:hover, .menu-panel .panel-body-bottom .btn:focus-visible, .menu-panel .panel-body-bottom .d-modal.json-schema-editor-modal .je-ready .json-editor-btn-add:focus-visible, .d-modal.json-schema-editor-modal .je-ready .menu-panel .panel-body-bottom .json-editor-btn-add:focus-visible, .menu-panel .panel-body-bottom .d-modal.json-schema-editor-modal .je-ready button:focus-visible, .d-modal.json-schema-editor-modal .je-ready .menu-panel .panel-body-bottom button:focus-visible {
	background: transparent !important;
}
.discourse-no-touch .btn:active .d-icon, .discourse-no-touch .btn.btn-active .d-icon, .btn:active .d-icon, .d-modal.json-schema-editor-modal .je-ready button:active .d-icon, .d-modal.json-schema-editor-modal .je-ready .json-editor-btn-add:active .d-icon, .btn.btn-active .d-icon, .d-modal.json-schema-editor-modal .je-ready button.btn-active .d-icon, .d-modal.json-schema-editor-modal .je-ready .btn-active.json-editor-btn-add .d-icon {
	color: red !important;
}
.discourse-no-touch .btn:active:not(:hover, :focus), .discourse-no-touch .btn.btn-active:not(:hover, :focus), .btn:active:not(:hover, :focus), .d-modal.json-schema-editor-modal .je-ready button:active:not(:hover, :focus), .d-modal.json-schema-editor-modal .je-ready .json-editor-btn-add:active:not(:hover, :focus), .btn.btn-active:not(:hover, :focus), .d-modal.json-schema-editor-modal .je-ready button.btn-active:not(:hover, :focus), .d-modal.json-schema-editor-modal .je-ready .btn-active.json-editor-btn-add:not(:hover, :focus) {
	background-image: none !important;
}
.drop-down-mode .d-header-icons .active .icon, .drop-down-mode .d-header-icons .header-color-scheme-toggle .-expanded {
	background-color: transparent !important;

}
.user-menu.revamped .bottom-tabs {
	border-top: none !important;
}
.user-menu.revamped .menu-tabs-container {
	background: rgba(0, 0, 0, 0.02) !important;
}
.user-menu.revamped .quick-access-panel {
	border: none !important;
}
.empty-state .empty-state-title,
.empty-state .empty-state-body {
	font-size: 14px !important;
}
.user-menu .quick-access-panel .item-label, .user-notifications-list .item-label {
	font-size: 14px !important;
}
.d-header-icons .icon {
	width: 3em !important;
	height: 3em !important;
	border: none !important;
}
.discourse-no-touch .d-header-icons .icon:hover, .discourse-no-touch .d-header-icons .icon:focus {
	background-color: transparent !important;
}
.d-header-icons .icon img.avatar {
	width: 3em !important;
	border: 3px solid rgba(0, 0, 0, 0.8) !important;
}
.user-menu .quick-access-panel .read, .user-notifications-list .read {
	background-color: transparent !important;
}

#post_1 .topic-body, #post_1 .topic-avatar,
img.avatar, img.prefix-image {
	border: none !important;
}

img.avatar {
	border-radius: 10px !important;
}

.list-controls .combo-box .combo-box-header {
	border-radius: 12px !important;
  color: #333 !important;
	border: 1px solid rgba(255, 255, 255, 0.5) !important;
	background: rgba(255, 255, 255, 0.5) !important;
}

.select-kit.combo-box.tag-drop .tag-drop-header, .select-kit.combo-box.tag-drop .selected-name {
  color: #333 !important;
}

.select-kit .select-kit-header {
	border: none !important;
	font-size: 14px!important;
	font-weight: bold !important;
	border-radius: 10px !important;
}

.search-menu .search-input, .search-menu-container .search-input {
	border-radius: 12px !important;
	border: 1px solid rgba(255, 255, 255, 0.9) !important;
	background: rgba(255, 255, 255, 0.7) !important;
}
.header-search--enabled .floating-search-input .search-banner-inner.wrap .search-menu .results, .search-header--visible .floating-search-input .search-banner-inner.wrap .search-menu .results {
	background: transparent !important;
}
.header-sidebar-toggle button:focus:hover, .discourse-no-touch .header-sidebar-toggle button:hover {
	background-color: transparent !important;
}

.menu-panel {
	border-radius: 12px !important;
	border: 1px solid rgba(255, 255, 255, 0.9) !important;
	box-shadow: 0 1px 50px rgba(0, 0, 0, 0.1) !important;
	background: rgba(255, 255, 255, 0.7) !important;
	backdrop-filter: blur(20px) saturate(160%) !important;
	margin: 1px 0 !important;
}

.welcome-banner__wrap .results {
	background: transparent !important;
}

.search-random-quick-tip .tip-label {
	background-color: rgba(0, 0, 0, 0.1) !important;backdrop-filter:blur(20px) saturate(180%) !important;
	border-radius: 6px !important;
}

.search-menu .search-link .topic-title, .search-menu-container .search-link .topic-title {
	font-size: 16px !important;
	color: #000000 !important;
	line-height: 2.5;
}

search-menu .search-link:focus, .search-menu .search-link:hover, .search-menu-container .search-link:focus, .search-menu-container .search-link:hover {
	background-color: #E3D6CF70 !important;
}

.search-menu .search-link, .search-menu-container .search-link {
	margin: 20px  !important;
	border-radius: 10px !important;
}


.sidebar-wrapper {
	margin-left: 55px !important;
	border-radius: 12px !important;
	background: rgba(255, 255, 255, 0.7) !important;
}
.sidebar-wrapper .sidebar-container {
	border-right: none !important;
}

.sidebar-section-link-wrapper .sidebar-section-link:focus, .sidebar-section-link-wrapper .sidebar-section-link:hover {
	background: rgba(0, 0, 0, 0.05) !important;
	border-radius: 8px !important;
}
.sidebar-section-wrapper {
	border-bottom: none !important;
}

.sidebar-section-wrapper .sidebar-section-header-wrapper {
	margin-top: 20px !important;
}

.sidebar-section-link-wrapper .sidebar-section-link {
	font-size: 15px;
}
.sidebar-footer-wrapper {
	background: none !important;
}
/* 内页帖子卡片式设计 (保留) */
.topic-post, .crawler-post {
  background-color: rgba(255, 255, 255, 0.8) !important;
  border-radius: 16px !important;
  margin-bottom: 15px !important;
  border: 1px solid rgba(255, 255, 255, 0.9) !important;
  overflow: hidden !important;
  padding: 10px !important;
  border: 1px solid rgba(255, 255, 255, 0.3) !important;
  position: relative !important;
}

.topic-post article.boxed, .crawler-post article.boxed {
  border: none !important;
  box-shadow: none !important;
}

.topic-avatar, .crawler-post-meta .creator {
  border-top: none !important;
  padding: 15px 10px 15px 15px !important;
}

.topic-body, .crawler-post .post {
  padding: 15px 15px 10px 0 !important;
}

.topic-meta-data, .crawler-post-meta {
  padding-bottom: 8px !important;
  margin-bottom: 10px !important;
}
.topic-meta-data {
    display: flex !important;
    align-items: flex-start !important;
    flex-direction: column !important;
}

.d-header #site-logo {
	height: var(--d-logo-height)!important;
	width: auto !important;
	max-width: 100% !important;
}

.header-sidebar-toggle button .d-icon {
	width: 100%;
	display: inline-block !important;
	color: rgba(0, 0, 0, 0.8) !important;
}

/* 爬虫模式特殊处理 (保留) */
.crawler-post {
  display: flex !important;
  flex-direction: column !important;
}

.crawler-post-meta {
  display: flex !important;
  align-items: center !important;
  padding: 10px 15px !important;
  background-color: rgba(245, 245, 245, 0.5) !important;
}

.crawler-post .post {
  padding: 15px !important;
}

/* 确保图片视频不溢出 (保留) */
img, video { /* 应用于更广泛的图片/视频，而不仅仅是回复卡片内 */
  max-width: 100% !important;
  height: auto !important;
}


body {
  background: #b0bfd2;
  background-attachment: fixed !important;
}

/*--------标题栏--------*/
.d-header-wrap {
	position: relative !important;
}

.d-header {
	background-color: rgba(255, 255, 255, 0.5)!important;
  box-shadow: 0 1px 20px rgba(0, 0, 0, 0.05) !important
  border-bottom: 1px solid rgba(255, 255, 255, 0.3) !important;
}

/*--------主页--------*/
#main-outlet-wrapper {
	background: rgba(255, 255, 255, 0.5) !important;
	border: 1px solid rgba(255, 255, 255, 0.3) !important;
  margin-top: 40px !important;
  border-radius: 16px !important;
  width: 80% !important;
  overflow: hidden;
}

.anon .topic-list-main-link a.title:visited:not(.badge-notification), .anon .topic-list .main-link a.title:visited:not(.badge-notification), .topic-list .anon .main-link a.title:visited:not(.badge-notification) {
	color: #333 !important;
}

.discourse-tag {
	border-radius: 4px;
}

#list-area .show-more {
	top: -30px;
}

#list-area .show-more .alert {
	padding: 5px !important;
	font-size: 14px !important;
	display: block !important;
	background-color: rgba(0, 0, 0, 0.5) !important;
	border-radius: 8px !important;
	color: #fff !important;
	display: block !important;
	text-align: center;
}
.nav-pills > li a.active, .nav-pills > li button.active {
	color: #ca4e4e  !important;
}
/*--------帖子列表布局修改--------*/
/* 修改帖子列表项的布局 */
.topic-list {
  border-collapse: separate !important;
  border-spacing: 0 8px !important;
  padding: 0 20px;
}

.topic-list-item {
	display: flex !important;
	padding: 5px 10px !important;
	align-items: flex-start !important;
	background-color: rgba(255, 255, 255, 0.9) !important;
	margin:25px 12px !important;
	border-radius: 16px !important;
	transition: all 0.2s ease !important;
	position: relative !important;
	border: 1px solid rgba(255, 255, 255, 0.8) !important;
}

.topic-list-item, tr {
	border-bottom: none !important;
}

.topic-list-main-link a.title, .latest-topic-list-item .main-link a.title, .topic-list .main-link a.title {
	font-size: 15px;
}

.topic-list .topic-excerpt {
	font-size: 13px !important;
}

.topic-list .link-bottom-line a.discourse-tag.box {
	font-size: 12px;
}

.badge-category__wrapper .badge-category__name {
	color: #333 !important;
	font-size: 11px !important;
}

.topic-list .link-bottom-line {
	font-size: var(--font-down-1);
	margin-top: 0 !important;
  gap: 0 .5em !important;
  line-height: inherit !important;
}

.topic-list .posters {
	width: auto !important;
}

/* 隐藏原始表格结构 */
.topic-list-item td {
  border: none !important;
  padding: 0 !important;
}

/* 隐藏不需要的单元格 */
.topic-list-item td.posters-names,
.topic-list-item td.activity,
.topic-list-item td.age,
.topic-list-item td.views,
.topic-list-item td.posts-map,
.topic-list-item td.posts {
  display: none !important;
}

/* 显示并定位头像 - 确保在左侧 */
.topic-list-item td.posters {
	display: block !important;
	margin-right: 12px !important;
	position: absolute !important;
	left: 20px !important;
	top: 14px !important;
}

.topic-list-item td.posters a:first-child {
  display: block !important;
}

.topic-list-item td.posters a:not(:first-child) {
  display: none !important;
}

.topic-list-item img.avatar {
	width: 42px !important;
	border-radius: 10px !important;
	box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
	border: 2px solid rgba(0, 0, 0, 0.8) !important;
}

/* 创建右侧内容区域 */
.topic-list-item td.main-link {
  display: flex !important;
  flex-direction: column !important;
  flex: 1 !important;
  padding: 0 !important;
  margin-left: 70px !important; /* 为头像留出空间 */
  position: relative !important;
  line-height: 2;
}

/* 标题样式 */
.topic-list-item .main-link a.title {
	font-size: 15px !important;
	padding: 0 !important;
	font-weight: bold !important;
	color: #333 !important;
	display: block !important;
}

/* 隐藏原始元数据元素，防止它们在被移动到容器前显示在错误位置 */
.topic-list-item td.num.views,
.topic-list-item .topic-list-data.heatmap-low,
.topic-list-item td.num.posts,
.topic-list-item td.num.posts-map.posts,
.topic-list-item td.num.activity,
.topic-list-item td.activity.num.topic-list-data.age {
  visibility: hidden !important;
  position: absolute !important;
  top: -9999px !important;
  left: -9999px !important;
}

/* 当元素被移动到元数据容器后恢复可见性 */
.topic-metadata-container .topic-metadata-item {
  visibility: visible !important;
  position: static !important;
  top: auto !important;
  left: auto !important;
}

/* 通用图标样式 */
.topic-list-item .icon-before:before {
  margin-right: 4px !important;
  font-size: 14px !important;
  color: #333 !important;
  font-family: sans-serif !important;
  display: inline-block !important;
  width: 14px !important;
  height: 14px !important;
  background-size: contain !important;
  background-repeat: no-repeat !important;
  background-position: center !important;
  vertical-align: middle !important;
}

/* 元数据容器 */
.topic-list-item .topic-metadata-container {
  display: flex !important;
  flex-wrap: wrap !important;
  align-items: center !important;
  gap: 16px !important;
  position: relative !important;
  width: 100% !important;
  box-sizing: border-box !important;
  left: 0 !important;
  right: 0 !important;
}

/* 元数据项通用样式 */
.topic-list-item .topic-metadata-item {
  display: inline-flex !important;
  align-items: center !important;
  font-size: 13px !important;
  color: #666 !important;
  white-space: nowrap !important;
}

/* 显示作者名字 */
.topic-list-item .topic-author-name {
  display: inline-flex !important;
  align-items: center !important;
}

.topic-list-item .topic-author-name:before {
  content: "" !important;
  margin-right: 4px !important;
  width: 14px !important;
  height: 14px !important;
  display: inline-block !important;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23333'%3E%3Cpath d='M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z'/%3E%3C/svg%3E") !important;
  background-size: contain !important;
  background-repeat: no-repeat !important;
  background-position: center !important;
  vertical-align: middle !important;
}

/* 显示浏览量 */
.topic-list-item td.num.views {
  display: inline-flex !important;
  align-items: center !important;
}

.topic-list-item td.num.views:before {
  content: "" !important;
  margin-right: 4px !important;
  width: 14px !important;
  height: 14px !important;
  display: inline-block !important;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23333'%3E%3Cpath d='M12 4.5C7 4.5 2.73 7.61 1 12c1.73 4.39 6 7.5 11 7.5s9.27-3.11 11-7.5c-1.73-4.39-6-7.5-11-7.5zM12 17c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5zm0-8c-1.66 0-3 1.34-3 3s1.34 3 3 3 3-1.34 3-3-1.34-3-3-3z'/%3E%3C/svg%3E") !important;
  background-size: contain !important;
  background-repeat: no-repeat !important;
  background-position: center !important;
  vertical-align: middle !important;
}

/* 显示回复数 */
.topic-list-item td.num.posts {
  display: inline-flex !important;
  align-items: center !important;
}

.topic-list .num.posts a {
  padding: 0 !important;
}

.topic-list-item td.num.posts:before {
  content: "" !important;
  margin-right: 4px !important;
  width: 14px !important;
  height: 14px !important;
  display: inline-block !important;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23333'%3E%3Cpath d='M20 2H4c-1.1 0-2 .9-2 2v18l4-4h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2z'/%3E%3C/svg%3E") !important;
  background-size: contain !important;
  background-repeat: no-repeat !important;
  background-position: center !important;
  vertical-align: middle !important;
}

/* 显示最后回复时间 */
.topic-list-item td.last-post {
  display: inline-flex !important;
  align-items: center !important;
}

.topic-list-item td.last-post:before {
  content: "" !important;
  margin-right: 4px !important;
  width: 14px !important;
  height: 14px !important;
  display: inline-block !important;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23333'%3E%3Cpath d='M11.99 2C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2zM12 20c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8zm.5-13H11v6l5.25 3.15.75-1.23-4.5-2.67z'/%3E%3C/svg%3E") !important;
  background-size: contain !important;
  background-repeat: no-repeat !important;
  background-position: center !important;
  vertical-align: middle !important;
}

.topic-list .num.activity a {
  padding: 0 !important;
}

/* 显示创建/更新日期 */
.topic-list-item td.activity.num.topic-list-data.age {
  display: inline-flex !important;
  align-items: center !important;
  white-space: nowrap !important;
}

.topic-list-item td.activity.num.topic-list-data.age:before {
  content: "" !important;
  margin-right: 4px !important;
  width: 16px !important;
  height: 16px !important;
  min-width: 16px !important;
  min-height: 16px !important;
  max-width: 16px !important;
  max-height: 16px !important;
  display: inline-block !important;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='%23000000'%3E%3Cpath d='M12 2C6.5 2 2 6.5 2 12s4.5 10 10 10 10-4.5 10-10S17.5 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm.5-13H11v6l5.2 3.2.8-1.3-4.5-2.7V7z'/%3E%3C/svg%3E") !important;
  background-size: 16px 16px !important;
  background-repeat: no-repeat !important;
  background-position: center !important;
  vertical-align: middle !important;
  opacity: 1 !important;
  transform: scale(1) !important;
  zoom: 1 !important;
  -webkit-transform: scale(1) !important;
  -moz-transform: scale(1) !important;
  -ms-transform: scale(1) !important;
  -o-transform: scale(1) !important;
}

/* 显示分类 */
.topic-list-item td.category {
  display: inline-flex !important;
  align-items: center !important;
  position: absolute !important;
  right: 12px !important;
  bottom: 12px !important;
}

.topic-list-item td.category:before {
  content: "" !important;
  margin-right: 4px !important;
  width: 14px !important;
  height: 14px !important;
  display: inline-block !important;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23333'%3E%3Cpath d='M17.63 5.84C17.27 5.33 16.67 5 16 5L5 5.01C3.9 5.01 3 5.9 3 7v10c0 1.1.9 1.99 2 1.99L16 19c.67 0 1.27-.33 1.63-.84L22 12l-4.37-6.16z'/%3E%3C/svg%3E") !important;
  background-size: contain !important;
  background-repeat: no-repeat !important;
  background-position: center !important;
  vertical-align: middle !important;
}

.topic-list-item .discourse-tag {
  font-size: 11px !important;
  padding: 2px 6px !important;
  margin-right: 4px !important;
  border-radius: 4px !important;
  background-color: transition !important;
}

.discourse-tag.box {
	background-color: transparent !important;
}

#reply-control {
	border-radius: 16px !important;
	overflow: hidden;
}

/* 确保表头不受影响 */
#global-notice-alert-global-notice,
.svg-icon-title,
.topic-list-header {
  display: none !important;
}

.topic-statuses {
	display: none !important;
}

/* 确保所有图标不随滚动变化 */
.topic-list-item *:before,
.topic-list-item *::before {
  transform: scale(1) !important;
  zoom: 1 !important;
  -webkit-transform: scale(1) !important;
  -moz-transform: scale(1) !important;
  -ms-transform: scale(1) !important;
  -o-transform: scale(1) !important;
}

/* 确保时间图标正确显示且不随滚动变化 */
td.activity.num.topic-list-data.age:before,
td.activity.num.topic-list-data.age::before {
  content: "" !important;
  margin-right: 4px !important;
  width: 16px !important;
  height: 16px !important;
  min-width: 16px !important;
  min-height: 16px !important;
  max-width: 16px !important;
  max-height: 16px !important;
  display: inline-block !important;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='%23000000'%3E%3Cpath d='M12 2C6.5 2 2 6.5 2 12s4.5 10 10 10 10-4.5 10-10S17.5 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm.5-13H11v6l5.2 3.2.8-1.3-4.5-2.7V7z'/%3E%3C/svg%3E") !important;
  background-size: 16px 16px !important;
  background-repeat: no-repeat !important;
  background-position: center !important;
  vertical-align: middle !important;
  opacity: 1 !important;
  transform: scale(1) !important;
  zoom: 1 !important;
  -webkit-transform: scale(1) !important;
  -moz-transform: scale(1) !important;
  -ms-transform: scale(1) !important;
  -o-transform: scale(1) !important;
}

/* 确保元数据容器中的所有元素都有正确的样式 */
.topic-metadata-container .topic-metadata-item {
  margin-right: 0 !important;
  padding: 0 !important;
  position: static !important;
  left: auto !important;
  right: auto !important;
  bottom: auto !important;
  top: auto !important;
  display: inline-flex !important;
  align-items: center !important;
  font-size: 12px !important;
  color: #666 !important;
}

/* 添加平滑过渡效果 */
.topic-metadata-container {
  transition: opacity 0.2s ease !important;
}

/* 确保linux-do-beautify类应用后立即隐藏原始元素 */
.linux-do-beautify .topic-list-item td.num.views,
.linux-do-beautify .topic-list-item .topic-list-data.heatmap-low,
.linux-do-beautify .topic-list-item td.num.posts,
.linux-do-beautify .topic-list-item td.num.posts-map.posts,
.linux-do-beautify .topic-list-item td.num.activity,
.linux-do-beautify .topic-list-item td.activity.num.topic-list-data.age {
  visibility: hidden !important;
  position: absolute !important;
  top: -9999px !important;
  left: -9999px !important;
}

/* 为各种图标添加样式 */
.topic-metadata-item .views-icon,
.topic-metadata-item .heatmap-icon,
.topic-metadata-item .posts-icon,
.topic-metadata-item .activity-icon,
.topic-metadata-item .age-icon {
  display: inline-block !important;
  width: 14px !important;
  height: 14px !important;
  margin-right: 4px !important;
  background-size: contain !important;
  background-repeat: no-repeat !important;
  background-position: center !important;
  vertical-align: middle !important;
}

.fk-d-menu__inner-content {
	background-color: transparent;
	border-radius: 12px !important;
  background: rgba(255, 255, 255, 0.8) !important;
	box-shadow: 0 1px 40px rgba(0, 0, 0, 0.1) !important;
	border: 1px solid rgba(255, 255, 255, 0.4) !important;
	backdrop-filter: blur(20px) saturate(180%) !important;
  font-size: 14px;
}

.user-card .badge-section .user-badge, .user-card .badge-section .more-user-badges a {
	background: rgba(0, 0, 0, 0.05) !important;
	border-radius: 8px !important;
	font-size: 13px !important;
  border: none !important;
}

.user-card .metadata, .group-card .metadata {
	font-size: 14px  !important;
}

.btn.btn-icon-text.btn-default {
	font-size: 14px !important;
	background: rgba(0, 0, 0, 0.05) !important;
	border-radius: 10px !important;
}

.user-card, .group-card {
	background: transparent !important;
}

.user-card-avatar .avatar-flair.rounded, .user-profile-avatar .avatar-flair.rounded {
	display: none  !important;
}

.user-card .card-content, .group-card .card-content {
	padding: 30px  !important;
	background: transparent  !important;
}

/* 浏览量图标 */
.topic-metadata-item .views-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23333'%3E%3Cpath d='M12 4.5C7 4.5 2.73 7.61 1 12c1.73 4.39 6 7.5 11 7.5s9.27-3.11 11-7.5c-1.73-4.39-6-7.5-11-7.5zM12 17c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5zm0-8c-1.66 0-3 1.34-3 3s1.34 3 3 3 3-1.34 3-3-1.34-3-3-3z'/%3E%3C/svg%3E") !important;
}

/* 回复数图标 */
.topic-metadata-item .posts-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23333'%3E%3Cpath d='M20 2H4c-1.1 0-2 .9-2 2v18l4-4h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2z'/%3E%3C/svg%3E") !important;
}

/* 活动时间图标 */
.topic-metadata-item .activity-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23333'%3E%3Cpath d='M11.99 2C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2zM12 20c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8zm.5-13H11v6l5.25 3.15.75-1.23-4.5-2.67z'/%3E%3C/svg%3E") !important;
}

/* 创建时间图标 */
.topic-metadata-item .age-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='%23000000'%3E%3Cpath d='M12 2C6.5 2 2 6.5 2 12s4.5 10 10 10 10-4.5 10-10S17.5 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm.5-13H11v6l5.2 3.2.8-1.3-4.5-2.7V7z'/%3E%3C/svg%3E") !important;
}

/* 热度图标 */
.topic-metadata-item .heatmap-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23333'%3E%3Cpath d='M13.5.67s.74 2.65.74 4.8c0 2.06-1.35 3.73-3.41 3.73-2.07 0-3.63-1.67-3.63-3.73l.03-.36C5.21 7.51 4 10.62 4 14c0 4.42 3.58 8 8 8s8-3.58 8-8C20 8.61 17.41 3.8 13.5.67zM11.71 19c-1.78 0-3.22-1.4-3.22-3.14 0-1.62 1.05-2.76 2.81-3.12 1.77-.36 3.6-1.21 4.62-2.58.39 1.29.59 2.65.59 4.04 0 2.65-2.15 4.8-4.8 4.8z'/%3E%3C/svg%3E") !important;
}

/*--------- 内页 ----------*/
#topic-title .title-wrapper {
	padding: 0 30px;
}

.topic-category {
	margin: 10px 0;
}

.discourse-tag.box {
	font-size: 12px;
}

.container.posts {
	grid-template-columns: 100% 25% !important;
  padding: 30px;
}

article[id^="post_"] img.avatar {
	border: 2px solid rgba(0, 0, 0, 0.8) !important;
}

body .reply-to-tab img.avatar {
	border: 1px solid rgba(0, 0, 0, 0.8) !important;
	border-radius: 5px !important;
  margin: 0 10px;
}

.topic-body {
  width: 90% !important;
	border-top: none !important;
}

.topic-body .cooked {
	padding: 0 var(--topic-body-width-padding) !important;
	font-size: 15px !important;
}

.topic-map__contents .topic-map__stats.--single-stat button span, .post-info a, .post-info svg {
	font-size: 12px !important;
	color: var(--tertiary) !important;
}

.topic-map.--op {
	border-top: none !important;
}

.topic-map__stats {
	font-size: 13px;
}

.topic-status-info, .topic-timer-info {
	border-top: none !important;
}

.cooked img:not(.thumbnail, .ytp-thumbnail-image, .emoji), .d-editor-preview img:not(.thumbnail, .ytp-thumbnail-image, .emoji) {
	border-radius: 12px !important;
}

.reply-to-tab {
	display: none !important;

}.topic-meta-data .post-infos {
	align-items: end !important;
  font-size: 13px;
}

aside.quote .title {
	color: inherit !important;
	border-left: 0 !important;
	background-color: transparent !important;
	user-select: none;
}

blockquote {
  margin: 0 !important;
	border-left: none !important;
	background-color: transparent !important;
}

aside.quote {
	margin-bottom: 10px !important;
	padding: 8px 12px !important;
	background-color: rgba(0, 0, 0, 0.05)!important;
	border-left: 0 !important;
	border-radius: 10px !important;
	font-size: 14px !important;
}

.reply-quote {
    margin-bottom: 10px !important;
    padding: 8px 12px;
    background-color: rgba(0, 0, 0, 0.05) !important;
    border-left: 0 !important;
    border-radius: 16px;
    font-size: 14px;
}

.cooked img:not(.thumbnail, .ytp-thumbnail-image, .emoji), .d-editor-preview img:not(.thumbnail, .ytp-thumbnail-image, .emoji) {
	border-radius: 6px !important;
}

.names span.first > a {
	color: #000 !important;
}

.second.username > a {
	color: #fff;
	background: rgba(0, 0, 0, .1);
	border-radius: 6px !important;
	font-size: 12px;
	padding: 0 6px;
}

.small-action .small-action-desc {
	box-sizing: border-box;
	display: block !important;
	flex-wrap: wrap;
	color: #000 !important;
	padding: 1em 0 !important;
	width: 100% !important;
	min-width: 0;
	border-top: 1px solid #fff !important;
	text-align: center;
}

.small-action .topic-avatar {
	display: none !important;
}

.lightbox-wrapper .lightbox {
    outline: 5px solid rgba(0, 0, 0, 0.1) !important;
    border-radius: 12px !important;
    margin: 10px;
}

aside.onebox {
	border-radius: 12px !important;
}

.topic-avatar .avatar-flair.rounded, .avatar-flair-preview .avatar-flair.rounded, .collapsed-info .user-profile-avatar .avatar-flair.rounded, .user-image .avatar-flair.rounded, .latest-topic-list-item .avatar-flair.rounded {
	display: none !important;
}

.topic-map {
	max-width: 98% !important;
}

.discourse-reactions-counter {
	font-size: 11PX;
}

.topic-avatar {
	width: 48px !important;
}

.fk-d-menu {
	margin: 10px;
}

.user-card .names__secondary, .user-card [class*="metadata__"], .group-card .names__secondary, .group-card [class*="metadata__"],
.user-card .card-row:not(.first-row), .group-card .card-row:not(.first-row) ,
.user-card .card-content .bio, .group-card .card-content .bio {
	font-size: 14px !important;
}

#user-card img.avatar,
#user-card img.prefix-image {
  border-radius: 16px !important;
	border: 4px solid rgba(0, 0, 0, 0.8) !important;
}

.user-card .first-row .names, .group-card .first-row .names {
	padding-left: 20px !important;
}

.topic-meta-data .user-status-message-wrap img.emoji {

	display: none;
}
/* 楼主标签样式 - 使用伪元素 */
div.topic-avatar::after {
    content: "";
    display: none;
}

div.topic-owner .topic-avatar::after {
	content: "楼主";
	display: block;
	background-color: #e74c3c;
	color: white;
	font-size: 10px;
	font-weight: bold;
	padding: 2px 6px;
	border-radius: 6px;
	text-align: center;
	width: 20px;
	margin: 15px auto 0 auto;
}

.topic-map .--users-summary {
    gap: 0.5em !important;
}

.topic-map__contents .topic-map__stats.--single-stat button span, .post-info a, .post-info svg {
	font-size: 12px !important;
	color: rgba(0, 0, 0, 0.5) !important;
	padding: 5px 0 !important;
	display: block !important;
}

.topic-map__contents .topic-map__stats .fk-d-menu__trigger .number {
	color: #000 !important;
}

.topic-map .--users-summary .avatar {
	border-radius: 8px !important;
	border: 2px solid rgba(0, 0, 0, 0.8) !important;
}

.sidebar-section-link-wrapper .sidebar-section-link--active, .sidebar-section-link-wrapper .sidebar-section-link.active {
	background: rgba(0, 0, 0, 0.05) !important;
	border-radius: 8px !important;
}

/* 隐藏原始的楼主标识 */
div.topic-owner .topic-body .contents > .cooked::after {
    display: none !important;
}

code {
	background: rgba(0, 0, 0, 0.07)!important;
	font-size: 14px;
	border-radius: 10px !important;
}

.more-topics__container .topic-list-item td.main-link {
	padding: 0 10px !important;
  margin-left: 0 !important;
}

.post-links-container .track-link span:not(.badge) {
	font-size: 14px;
}

.discourse-reactions-list .reactions .discourse-reactions-list-emoji .emoji {
	width: 0.9em !important;
	height: 0.9em !important;
}

nav.post-controls .actions {
	font-size: 13px  !important;
}

/* --------登录页----------*/
input[type="text"], input[type="password"], input[type="datetime"], input[type="datetime-local"], input[type="date"], input[type="month"], input[type="time"], input[type="week"], input[type="number"], input[type="email"], input[type="url"], input[type="search"], input[type="tel"], input[type="color"] {
	background: transparent !important;
	border: none ;

}

.login-fullpage #login-buttons .btn-social, .signup-fullpage #login-buttons .btn-social, .invites-show #login-buttons .btn-social, .password-reset-page #login-buttons .btn-social {
	padding: .75em .77em;
	border-radius: 8px !important;
	border: 1px solid rgba(255, 255, 255, 0.5) !important;
	box-shadow: 0 0px 20px rgba(0, 0, 0, 0.05), inset 0px 0px 30px 10px rgba(255, 255, 255, 0.3) !important;
	background: rgba(255, 255, 255, 0.5) !important;
}

.login-fullpage .login-page-cta__existing-account::before, .login-fullpage .login-page-cta__no-account-yet::before, .login-fullpage .signup-page-cta__existing-account::before, .login-fullpage .signup-page-cta__no-account-yet::before, .signup-fullpage .login-page-cta__existing-account::before, .signup-fullpage .login-page-cta__no-account-yet::before, .signup-fullpage .signup-page-cta__existing-account::before, .signup-fullpage .signup-page-cta__no-account-yet::before, .invites-show .login-page-cta__existing-account::before, .invites-show .login-page-cta__no-account-yet::before, .invites-show .signup-page-cta__existing-account::before, .invites-show .signup-page-cta__no-account-yet::before {
	background-color: transparent !important;
}

.btn-primary {
	border-radius: 8px !important;
}

/* ========== 屏蔽功能样式 ========== */
.filter-button {
	position: fixed;
	bottom: 20px;
	right: 20px;
	padding: 10px 20px;
	background-color: #4CAF50;
	color: white;
	border: none;
	border-radius: 5px;
	cursor: pointer;
	z-index: 9999;
}
.filter-modal {
	display: none;
	position: fixed;
	bottom: 70px;
	right: 20px;
	background-color: white;
	padding: 20px;
	border-radius: 5px;
	box-shadow: 0 0 10px rgba(0,0,0,0.2);
	z-index: 9999;
	width: 300px;
}
.filter-input {
	width: 100%;
	padding: 8px;
	margin-bottom: 10px;
	border: 1px solid #ddd;
	border-radius: 4px;
}
.filter-list {
	max-height: 200px;
	overflow-y: auto;
	margin-bottom: 10px;
}
.filter-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 5px;
	border-bottom: 1px solid #eee;
}
.remove-keyword {
	color: red;
	cursor: pointer;
}
.filtered-topic {
	display: none !important;
	visibility: hidden !important;
	opacity: 0 !important;
	pointer-events: none !important;
	position: absolute !important;
	z-index: -9999 !important;
	transform: scale(0) !important;
	max-height: 0 !important;
	max-width: 0 !important;
	overflow: hidden !important;
}
/* 屏蔽按钮和触发区域样式 */
.block-list-button {
	position: fixed;
	bottom: 20px;
	right: 20px;
	font-size: 13px !important;
	z-index: 1000;
	padding: 6px 12px;
	background: #000;
	box-shadow: 0 0 6px rgba(0, 0, 0, 0.2);
	color: #fff;
	border: none;
	border-radius: 10px;
	cursor: pointer;
	pointer-events: none;
	transition: opacity 0.3s ease;
	font-weight: bold;
}

.trigger-area {
	position: fixed;
	bottom: 0;
	right: 0;
	width: 100px;
	height: 100px;
	z-index: 999;
}

.trigger-area:hover .block-list-button {
	opacity: 1;
	pointer-events: auto;
}

.block-dialog {
	position: fixed;
	top: 50%;
	left: 50%;
	transform: translate(-75%, -50%);
	width: 500px;
	max-width: 90vw;
	height: auto;
	max-height: 80vh;
	background: rgba(255, 255, 255, 0.5);
	padding: 20px;
	border-radius: 16px;
	box-shadow: 0 1px 50px rgba(0, 0, 0, 0.3);
	z-index: 1001;
	overflow: auto;
}

.block-dialog::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	border-radius: 16px;
	z-index: -1;
	backdrop-filter: blur(50px);
}

.block-title-bar {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	height: 30px;
	background: none;
	cursor: move;
	user-select: none;
}

.block-close-button {
	position: absolute;
	top: 12px;
	right: 12px;
	width: 16px;
	height: 16px;
	background: black;
	border: none;
	border-radius: 50%;
	cursor: pointer;
	transition: background 0.15s ease;
	box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
	overflow: hidden;
	text-indent: -9999px;
	padding: 0;
	z-index: 1002;
}

.block-close-button:hover {
	background: red;
}

.block-close-line {
	content: '';
	position: absolute;
	top: 50%;
	left: 50%;
	width: 8px;
	height: 2px;
	background: white;
	transition: background 0.15s ease;
}

.block-close-line-1 {
	transform: translate(-50%, -50%) rotate(45deg);
}

.block-close-line-2 {
	transform: translate(-50%, -50%) rotate(-45deg);
}

.block-list {
	margin: 20px 0 0 0;
	max-height: 220px;
	font-size: 12px;
	padding: 10px;
	background: #00000012;
	border-radius: 12px;
	color: #333;
	overflow-y: auto;
}

.block-keyword-list {
	margin: 15px 0 0 0;
	max-height: 100px;
}

.block-category-list {
	margin: 15px 0 0 0;
	max-height: 100px;
}

.block-input-container {
	display: flex !important;
	justify-content: center !important;
	align-items: center !important;
	margin: 15px 0 !important;
	width: 100% !important;
}

.block-input {
	flex: none !important;
	border: 1px solid #ddd !important;
	background: #ffffff96 !important;
	border-radius: 8px !important;
	font-size: 13px !important;
	padding: 5px 10px !important;
	width: 180px !important;
	margin: 10px !important;
	box-sizing: border-box !important;
}

.block-input:focus {
	border: 1px solid #999 !important;
	box-shadow: 0 1px 10px rgba(0, 0, 0, 0.1) !important;
	outline: none !important;
}

.block-button {
	flex: none;
	border-radius: 8px;
	border: 0px solid black;
	background: #000;
	padding: 7px 12px;
	color: white;
	font-size: 13px;
	cursor: pointer;
	box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
	white-space: nowrap;
	text-align: center;
}

.block-item {
	display: inline-block;
	margin: 0 5px 5px 0;
	border-radius: 4px;
}

.block-dialog h3 {
	margin: 0 0 10px;
	font-size: 14px;
	color: #000;
}

/* 头像容器样式 */
.avatar-container {
	position: relative; /* 为绝对定位的按钮提供相对定位容器 */
	display: inline-block; /* 或 block，取决于布局 */
	transition: margin-bottom 0.2s ease; /* 平滑过渡效果 */
	z-index: 10; /* 确保容器在其他元素之上 */
}

/* 头像下方的屏蔽按钮样式 */
.block-avatar-hover-button {
	display: none; /* 默认隐藏 */
	position: absolute;
	left: 50%; /* 水平居中 */
	transform: translateX(-50%); /* 水平居中对齐 */
	padding: 3px 6px; /* 增加按钮大小，更容易点击 */
	font-size: 10px; /* 增大字体 */
	background-color: rgba(0, 0, 0, 0.8); /* 黑色背景 */
	color: #fff; /* 白色文字 */
	border-radius: 6px;
	cursor: pointer;
	z-index: 100; /* 确保在其他元素之上 */
	box-shadow: 0px 1px 2px rgba(0,0,0,0.1);
	white-space: nowrap; /* 防止按钮文字换行 */
	opacity: 0; /* 初始透明 */
	bottom: -30px;
	transition: opacity 0.3s ease, transform 0.2s ease; /* 添加淡入淡出和变换效果 */
}
/* 创建一个隐形的扩展区域，使鼠标从头像移动到按钮时不会触发按钮消失 */
.avatar-container::before {
	content: '';
	position: absolute;
	left: 0;
	right: 0;
	bottom: -30px; /* 扩展到按钮下方 */
	height: 30px; /* 足够高以覆盖按钮区域 */
	background: transparent; /* 完全透明 */
	z-index: 99; /* 低于按钮和楼主标签 */
}

/* 当鼠标悬停在头像容器上时显示按钮 */
.avatar-container:hover .block-avatar-hover-button {
	display: inline-block; /* 在容器悬停时显示 */
	opacity: 1; /* 完全不透明 */
}

/* 当鼠标悬停在按钮上时保持显示并添加效果 */
.block-avatar-hover-button:hover {
	opacity: 1 !important; /* 确保按钮保持可见 */
	background-color: #000; /* 更深的黑色 */
	box-shadow: 0px 1px 4px rgba(0,0,0,0.1); /* 增强阴影 */
}

        `);
    }

    // ========== 工具函数 ==========
    function getFloorNumber(element) {
        // 优先从ID中提取
        if (element.id?.includes('_')) {
            const floorFromId = element.id.split('_')[1];
            return floorFromId;
        }

        // 从data-post-id属性中提取
        const postId = element.getAttribute('data-post-id');

        if (postId) {
            let floorNumber = postId.replace('top--', '');

            // 如果包含embedded-posts，尝试提取真实的楼层号
            if (floorNumber.includes('embedded-posts')) {
                // 尝试从embedded-posts__数字中提取数字
                const match = floorNumber.match(/embedded-posts__(\d+)/);
                if (match && match[1]) {
                    floorNumber = match[1];
                } else {
                    // 如果无法提取，返回空字符串
                    floorNumber = '';
                }
            }

            return floorNumber;
        }

        return '';
    }

    // 检测元素是否在视口附近
    function isElementNearViewport(element, buffer = CONFIG.VIEWPORT_BUFFER) {
        if (!element || !element.getBoundingClientRect) return false;

        const rect = element.getBoundingClientRect();
        const windowHeight = window.innerHeight;

        return (
            rect.bottom >= -buffer &&
            rect.top <= windowHeight + buffer
        );
    }

    // 检测是否为长帖子
    function detectLongPost() {
        const posts = document.querySelectorAll('article[id^="post_"]');
        isLongPost = posts.length > CONFIG.LONG_POST_THRESHOLD;
        if (isLongPost) {
            log(`检测到长帖子，共${posts.length}楼，启用性能优化模式`);
        }
        return isLongPost;
    }

    function cachePostContent(postId, content, author, floorNumber, avatarSrc) {
        if (!postId || !content) return;

        // 清理和标准化缓存键值
        const cleanPostId = cleanPostId_func(postId);
        const cacheKey = generateCacheKey(cleanPostId);

        postCache.set(cacheKey, {
            content,
            author: author || '未知用户',
            floorNumber: floorNumber || '',
            avatarSrc: avatarSrc || '',
            timestamp: Date.now()
        });
    }

    // 清理和标准化帖子ID
    function cleanPostId_func(postId) {
        if (!postId) return '';

        let cleanId = postId.toString();

        // 移除各种前缀
        cleanId = cleanId
            .replace(/^top--/, '') // 移除 top-- 前缀
            .replace(/^embedded-posts__/, '') // 移除 embedded-posts__ 前缀
            .replace(/embedded-posts__\d+/, '') // 移除 embedded-posts__数字
            .replace(/[^0-9]/g, ''); // 只保留数字

        return cleanId;
    }

    // 生成一致的缓存键值
    function generateCacheKey(cleanPostId) {
        return `post_${cleanPostId}`;
    }

    // 清理缓存中的错误映射
    function cleanupCache() {
        const now = Date.now();

        for (const [key, value] of postCache.entries()) {
            // 清理过期缓存
            if (now - value.timestamp > CONFIG.CACHE_TTL) {
                postCache.delete(key);
                continue;
            }

            // 检查缓存键值是否标准化
            if (!key.startsWith('post_') || key.includes('embedded-posts') || key.includes('top--')) {
                postCache.delete(key);
            }
        }
    }

    // ========== 统一的楼层号处理 ==========
    function updateFloorNumbers() {
        document.querySelectorAll('article[id^="post_"]').forEach(function (post) {
            if (!post.hasAttribute('data-floor-number')) { // 检查是否已经添加了楼层号
                const floorNum = getFloorNumber(post);
                if (floorNum) {
                    post.setAttribute('data-floor-number', floorNum);
                    log(`设置楼层号: ${post.id} -> #${floorNum}`);
                }
            }
        });
    }

    // ========== 智能队列处理 ==========
    function addToProcessingQueue(post, priority = 0) {
        if (!post || post.hasAttribute('data-quote-added')) return;

        // 在长帖子模式下，只处理视口附近的帖子
        if (isLongPost && !isElementNearViewport(post)) {
            post.setAttribute('data-quote-deferred', 'true');
            return;
        }

        processingQueue.push({ post, priority, timestamp: Date.now() });
        processingQueue.sort((a, b) => b.priority - a.priority);

        processQueue();
    }

    function processQueue() {
        if (concurrentProcessing >= CONFIG.MAX_CONCURRENT_PROCESSING || processingQueue.length === 0) {
            return;
        }

        const item = processingQueue.shift();
        if (!item || !item.post?.isConnected) {
            processQueue();
            return;
        }

        concurrentProcessing++;
        processPostOptimized(item.post).finally(() => {
            concurrentProcessing--;
            if (processingQueue.length > 0) {
                setTimeout(processQueue, CONFIG.IDLE_TIMEOUT);
            }
        });
    }

    // ========== 高速核心处理逻辑 - 优化引用加载速度 ==========
    async function processPostOptimized(post) {
        try {
            if (!post?.isConnected || post.hasAttribute('data-quote-added')) return;

            // 如果检测到网站原生加载，短暂延迟处理
            if (isNativeLoading) {
                log('检测到网站原生加载，短暂延迟处理帖子');
                setTimeout(() => {
                    if (post.isConnected && !isNativeLoading) {
                        processPostOptimized(post);
                    }
                }, CONFIG.NATIVE_LOADING_DELAY); // 使用配置的延迟时间
                return;
            }

            // 1. 优先使用闪电引用解析（若命中，再标记为已加入处理）
            if (lightningQuoteExtraction(post)) {
                post.setAttribute('data-quote-added', 'true');
                return;
            }

            // 2. 降级到传统处理
            const replyTab = post.querySelector('.reply-to-tab');
            const replyToPostId = replyTab?.getAttribute('aria-controls');
            if (!replyToPostId) return;

            log(`降级处理帖子 ${post.id} -> 目标 ${replyToPostId}`);

            // 3. 检查传统缓存 - 使用标准化的缓存键值
            const cleanPostId = cleanPostId_func(replyToPostId);
            const cacheKey = generateCacheKey(cleanPostId);
            const cachedPost = postCache.get(cacheKey);

            if (cachedPost) {
                post.setAttribute('data-quote-added', 'true');
                createQuoteInstantly(post, cachedPost);
                return;
            }

            // 4. 查找嵌入内容
            const embeddedPosts = post.querySelector('.embedded-posts.top');
            if (embeddedPosts) {
                post.setAttribute('data-quote-added', 'true');
                processEmbeddedReply(embeddedPosts, post, replyToPostId);
                return;
            }

            // 5. 查找页面上的父帖子
            const parentPost = findParentPost(replyToPostId);
            if (parentPost) {
                const postData = extractPostData(parentPost, replyToPostId);
                if (postData) {
                    // 使用标准化的缓存存储
                    const cleanId = cleanPostId_func(replyToPostId);
                    cachePostContent(cleanId, postData.content, postData.author, postData.floorNumber, postData.avatarSrc);
                    post.setAttribute('data-quote-added', 'true');
                    createQuoteInstantly(post, postData);
                    return;
                }
            }

            // 6. 恢复核心功能的网络请求策略 - 平衡功能与冲突避免
            const shouldFetch = !isNativeLoading && (
                !window.isScrolling || // 非滚动时
                isElementNearViewport(post, 0) || // 视口内
                (CONFIG.SMART_PRELOAD_ENABLED && isElementNearViewport(post, CONFIG.VIEWPORT_PRELOAD_DISTANCE)) // 预加载范围内
            );

            if (shouldFetch) {
                post.setAttribute('data-quote-added', 'true');
                fetchParentPostConcurrent(replyToPostId, post);
            } else {
                // 确定性延期，避免随机跳过导致的饥饿
                post.setAttribute('data-quote-deferred', 'true');
                post.removeAttribute('data-quote-added');
            }

        } catch (error) {
            log('处理帖子时出错:', error);
            post?.removeAttribute('data-quote-added');
        }
    }



    // ========== 辅助函数 ==========
    function findParentPost(replyToPostId) {
        const cleanId = cleanPostId_func(replyToPostId);
        return (
            document.querySelector(`#post_${cleanId}`) ||
            document.querySelector(`article[data-post-id="top--${cleanId}"]`) ||
            document.querySelector(`article[data-post-id$="__${cleanId}"]`)
        );
    }

    // ========== 智能用户名提取函数 ==========
    function extractCleanUsername(parentPost) {
        // 尝试多种选择器策略
        const selectors = [
            '.names .username',
            '[data-user-card]',
            '.username',
            '.names a',
            'a[href*="/u/"]',
            '.trigger-user-card'
        ];

        let foundElement = null;

        for (const selector of selectors) {
            foundElement = parentPost.querySelector(selector);
            if (foundElement) {
                break;
            }
        }

        if (!foundElement) {
            return '未知用户';
        }

        // 按优先级提取用户名
        let username = '';

        // 1. 优先使用 data-user-card 属性
        const dataUserCard = foundElement.getAttribute('data-user-card');
        if (dataUserCard) {
            if (!dataUserCard.includes('embedded-posts') && !dataUserCard.includes('#')) {
                username = dataUserCard.trim();
                return username;
            }
        }

        // 2. 尝试获取纯文本内容
        const textContent = foundElement.textContent || '';

        // 强化的清理逻辑，移除所有可能的前缀
        let cleanText = textContent
            .replace(/#embedded-posts__\d+\s*/g, '') // 移除 #embedded-posts__数字
            .replace(/embedded-posts__\d+\s*/g, '') // 移除 embedded-posts__数字（无#）
            .replace(/#embedded-posts[^\s]*\s*/g, '') // 移除其他 embedded-posts 相关内容
            .replace(/embedded-posts[^\s]*\s*/g, '') // 移除其他 embedded-posts 相关内容（无#）
            .replace(/^#\d+\s*/, '') // 移除开头的 #数字
            .replace(/^\s*#\s*/, '') // 移除开头的单独 #
            .trim();

        // 如果清理后仍然包含问题字符，进行更深度清理
        if (cleanText.includes('embedded-posts') || cleanText.includes('#')) {
            // 更激进的清理
            cleanText = cleanText
                .replace(/.*embedded-posts.*?\s+/g, '') // 移除包含embedded-posts的整个部分
                .replace(/.*#.*?\s+/g, '') // 移除包含#的整个部分
                .replace(/[#]/g, '') // 移除所有#字符
                .trim();
        }

        if (cleanText && cleanText.length > 0 && cleanText.length < 50) {
            username = cleanText;
            return username;
        }

        // 3. 尝试从子元素中获取
        const usernameSpan = foundElement.querySelector('.username, .trigger-user-card');
        if (usernameSpan) {
            const spanText = usernameSpan.textContent?.trim();
            if (spanText && !spanText.includes('embedded-posts') && !spanText.includes('#')) {
                username = spanText;
                return username;
            }
        }

        return '未知用户';
    }

    function extractPostData(parentPost, replyToPostId) {
        const content = parentPost.querySelector('.cooked');

        // 使用智能用户名提取函数
        const username = extractCleanUsername(parentPost, `extractPostData-${replyToPostId}`);

        const avatar = parentPost.querySelector('.topic-avatar img.avatar');

        if (!content) {
            return null;
        }

        // 安全地获取内容，避免使用innerHTML
        const contentClone = content.cloneNode(true);
        const result = {
            content: contentClone,
            author: username,
            floorNumber: getFloorNumber(parentPost) || replyToPostId.replace('top--', ''),
            avatarSrc: avatar?.getAttribute('src') || ''
        };

        return result;
    }

    // ========== 处理嵌入内容 ==========
    function processEmbeddedReply(embeddedPosts, post, replyToPostId) {
        try {
            embeddedPosts.style.display = 'none';

            const postData = extractPostData(embeddedPosts, replyToPostId);
            if (!postData) {
                log('嵌入帖子缺少必要元素');
                return;
            }

            // 获取楼层号 - 修复embedded-posts问题
            const dataPostId = embeddedPosts.getAttribute('data-post-id');
            if (dataPostId) {
                // 清理楼层号，移除embedded-posts前缀
                let cleanFloorNumber = dataPostId.replace('top--', '');

                // 如果包含embedded-posts，尝试提取真实的楼层号
                if (cleanFloorNumber.includes('embedded-posts')) {
                    // 尝试从embedded-posts__数字中提取数字
                    const match = cleanFloorNumber.match(/embedded-posts__(\d+)/);
                    if (match && match[1]) {
                        cleanFloorNumber = match[1];
                    } else {
                        // 如果无法提取，使用replyToPostId作为备选
                        const backupFloorNumber = replyToPostId.replace('top--', '');
                        if (!backupFloorNumber.includes('embedded-posts')) {
                            cleanFloorNumber = backupFloorNumber;
                        } else {
                            // 最后的备选：不显示楼层号
                            cleanFloorNumber = '';
                        }
                    }
                }

                postData.floorNumber = cleanFloorNumber;
            }

            if (replyToPostId) {
                const cleanId = cleanPostId_func(replyToPostId);
                cachePostContent(cleanId, ...Object.values(postData));
            }

            // 使用超高速引用创建
            createQuoteInstantly(post, postData);

            embeddedPosts.classList.add('processed');
            post.setAttribute('data-quote-processed', 'true');

            const replyTab = post.querySelector('.reply-to-tab');
            if (replyTab) replyTab.style.display = 'none';

        } catch (error) {
            log('处理嵌入回复时出错:', error);
        }
    }

    // ========== DOM操作安全检查 ==========
    function safeInsertBefore(parent, newNode, referenceNode) {
        try {
            if (!parent || !newNode || !parent.isConnected) {
                log('DOM插入失败：父节点或新节点无效');
                return false;
            }

            // 检查referenceNode是否有效
            if (referenceNode && referenceNode.nodeType === Node.ELEMENT_NODE && referenceNode.parentNode === parent) {
                parent.insertBefore(newNode, referenceNode);
            } else {
                // 如果referenceNode无效，直接appendChild
                parent.appendChild(newNode);
            }
            return true;
        } catch (error) {
            log('DOM插入出错，尝试appendChild:', error);
            try {
                parent.appendChild(newNode);
                return true;
            } catch (appendError) {
                log('appendChild也失败:', appendError);
                return false;
            }
        }
    }

    // ========== 多线程异步加载系统 ==========

    // 多线程请求管理器
    function createFetchRequest(replyToPostId, post) {
        return new Promise((resolve, reject) => {
            const cleanId = cleanPostId_func(replyToPostId);
            const timeoutId = setTimeout(() => {
                reject(new Error(`请求超时: ${replyToPostId}`));
            }, CONFIG.FETCH_TIMEOUT);

            fetchTimeouts.set(cleanId, timeoutId);

            try {
                const replyTab = post.querySelector('.reply-to-tab');
                if (!replyTab?.isConnected) {
                    clearTimeout(timeoutId);
                    fetchTimeouts.delete(cleanId);
                    reject(new Error('Reply tab not found'));
                    return;
                }

                // 创建兼容的点击事件
                let clickEvent;
                try {
                    clickEvent = new MouseEvent('click', {
                        bubbles: true,
                        cancelable: true
                    });
                } catch (e) {
                    clickEvent = document.createEvent('MouseEvent');
                    clickEvent.initMouseEvent('click', true, true, window, 0, 0, 0, 0, 0, false, false, false, false, 0, null);
                }

                replyTab.dispatchEvent(clickEvent);

                // 异步检查结果 - 优化重试机制
                let attempts = 0;
                const maxAttempts = Math.min(CONFIG.MAX_FETCH_ATTEMPTS * 2, 10); // 增加最大尝试次数但设置上限

                const checkForEmbeddedPosts = () => {
                    if (!post?.isConnected) {
                        clearTimeout(timeoutId);
                        fetchTimeouts.delete(cleanId);
                        reject(new Error(`Post disconnected: ${cleanId}`));
                        return;
                    }

                    if (++attempts > maxAttempts) {
                        clearTimeout(timeoutId);
                        fetchTimeouts.delete(cleanId);
                        log(`达到最大尝试次数，优雅降级: ${cleanId}`);

                        // 优雅降级：尝试从现有内容中提取
                        const embeddedPosts = post.querySelector('.embedded-posts.top');
                        if (embeddedPosts) {
                            const postData = extractPostData(embeddedPosts, cleanId);
                            if (postData) {
                                // 使用标准化的缓存存储
                                cachePostContent(cleanId, postData.content, postData.author, postData.floorNumber, postData.avatarSrc);
                                resolve(postData);
                                return;
                            }
                        }

                        // 如果无法提取，则静默失败而不是抛出错误
                        resolve(null);
                        return;
                    }

                    const embeddedPosts = post.querySelector('.embedded-posts.top');
                    const cookedElement = embeddedPosts?.querySelector('.cooked');
                    if (cookedElement && cookedElement.textContent?.trim()) {
                        clearTimeout(timeoutId);
                        fetchTimeouts.delete(cleanId);

                        embeddedPosts.style.display = 'none';
                        const postData = extractPostData(embeddedPosts, cleanId);

                        if (postData) {
                            // 获取楼层号 - 修复embedded-posts问题
                            const dataPostId = embeddedPosts.getAttribute('data-post-id');
                            if (dataPostId) {
                                // 清理楼层号，移除embedded-posts前缀
                                let cleanFloorNumber = dataPostId.replace('top--', '');

                                // 如果包含embedded-posts，尝试提取真实的楼层号
                                if (cleanFloorNumber.includes('embedded-posts')) {
                                    // 尝试从embedded-posts__数字中提取数字
                                    const match = cleanFloorNumber.match(/embedded-posts__(\d+)/);
                                    if (match && match[1]) {
                                        cleanFloorNumber = match[1];
                                    } else {
                                        // 如果无法提取，使用replyToPostId作为备选
                                const backupFloorNumber = cleanId;
                                        if (!backupFloorNumber.includes('embedded-posts')) {
                                            cleanFloorNumber = backupFloorNumber;
                                        } else {
                                            // 最后的备选：不显示楼层号
                                            cleanFloorNumber = '';
                                        }
                                    }
                                }

                                postData.floorNumber = cleanFloorNumber;
                            }

                            // 使用标准化的缓存存储
                            cachePostContent(cleanId, postData.content, postData.author, postData.floorNumber, postData.avatarSrc);
                            resolve(postData);
                        } else {
                            // 如果提取失败，继续尝试而不是立即失败
                            setTimeout(checkForEmbeddedPosts, CONFIG.FETCH_DELAY * Math.min(attempts, 3));
                        }
                    } else {
                        // 动态调整检查间隔，避免过于频繁的检查
                        const delay = CONFIG.FETCH_DELAY * Math.min(Math.ceil(attempts / 3), 5);
                        setTimeout(checkForEmbeddedPosts, delay);
                    }
                };

                checkForEmbeddedPosts();

            } catch (error) {
                clearTimeout(timeoutId);
                fetchTimeouts.delete(cleanId);
                reject(error);
            }
        });
    }

    // 恢复核心功能的并发请求管理器 - 平衡功能与冲突避免
    async function fetchParentPostConcurrent(replyToPostId, post) {
        return await safeExecuteAsync(async () => {
            const cleanId = cleanPostId_func(replyToPostId);
            // 基本检查，避免在明显的原生加载时冲突
        if (isNativeLoading) {
            post.removeAttribute('data-quote-added');
            setTimeout(() => {
                if (post.isConnected && !isNativeLoading) {
                    fetchParentPostConcurrent(cleanId, post);
                }
            }, CONFIG.NATIVE_LOADING_DELAY);
            return;
        }

            // 智能滚动时加载策略 - 恢复原有逻辑
        // 滚动中不随机丢弃，改为延后：由上层逻辑统一打 data-quote-deferred

        // 检查是否已有相同的请求在进行
        if (fetchPromises.has(cleanId)) {
            log(`复用现有请求: ${cleanId}`);
            try {
                const postData = await fetchPromises.get(cleanId);
                createQuoteInstantly(post, postData);
                return;
            } catch (error) {
                log(`复用请求失败: ${cleanId}`, error);
                post.removeAttribute('data-quote-added');
                return;
            }
        }

        // 优化的并发限制 - 长帖子也允许更多并发
        const maxConcurrent = isLongPost ? 6 : CONFIG.MAX_CONCURRENT_FETCHES;
        if (activeFetches >= maxConcurrent) {
            log(`达到并发限制，加入等待队列: ${replyToPostId}`);
            pendingFetches.push({ replyToPostId, post, timestamp: Date.now() });
            return;
        }

        // 开始新的请求
        activeFetches++;
        const fetchPromise = createFetchRequest(cleanId, post);
        fetchPromises.set(cleanId, fetchPromise);

        log(`开始高速并发请求 (${activeFetches}/${maxConcurrent}): ${cleanId}`);

        try {
            const postData = await fetchPromise;

            // 检查返回的数据是否有效
            if (postData && postData.content) {
                createQuoteInstantly(post, postData);
                post.setAttribute('data-quote-processed', 'true');

                // 隐藏reply-to-tab
                const replyTab = post.querySelector('.reply-to-tab');
                if (replyTab) replyTab.style.display = 'none';

                log(`成功处理引用: ${cleanId}`);
            } else {
                // 如果返回null或无效数据，静默处理
                log(`引用数据无效，静默跳过: ${cleanId}`);
                post.removeAttribute('data-quote-added');
            }

        } catch (error) {
            // 检查错误类型，避免对正常的null返回进行重试
            if (error && error.message && !error.message.includes('Max attempts reached')) {
                log(`并发请求失败: ${cleanId}`, error);

                // 优化重试策略
                const retryCount = post.getAttribute('data-retry-count') || '0';
                const retryNum = parseInt(retryCount);
                const maxRetries = Math.min(CONFIG.MAX_FETCH_ATTEMPTS, 2); // 减少重试次数避免过多错误

                if (retryNum < maxRetries && post.isConnected && !isNativeLoading) {
                    post.setAttribute('data-retry-count', (retryNum + 1).toString());
                    log(`准备重试请求 ${replyToPostId} (第${retryNum + 1}次)`);

                    // 指数退避重试策略
                    const retryDelay = CONFIG.RETRY_DELAY * Math.pow(2, retryNum);
                    setTimeout(() => {
                        if (post.isConnected && !isNativeLoading) {
                            post.removeAttribute('data-quote-added');
                            fetchParentPostConcurrent(cleanId, post);
                        }
                    }, retryDelay);
                } else {
                    post.removeAttribute('data-quote-added');
                    post.removeAttribute('data-retry-count');
                    log(`请求 ${cleanId} 达到最大重试次数，静默跳过`);
                }
            } else {
                // 对于达到最大尝试次数的情况，静默处理
                post.removeAttribute('data-quote-added');
                post.removeAttribute('data-retry-count');
                log(`请求 ${cleanId} 静默跳过`);
            }
        } finally {
            // 清理并处理等待队列
            activeFetches--;
            fetchPromises.delete(cleanId);

            // 快速处理等待队列
            if (pendingFetches.length > 0 && activeFetches < maxConcurrent) {
                const nextRequest = pendingFetches.shift();
                if (nextRequest.post.isConnected && !isNativeLoading) {
                    // 立即处理下一个请求，不延迟
                    setTimeout(() => {
                        fetchParentPostConcurrent(nextRequest.replyToPostId, nextRequest.post);
                    }, 10); // 最小延迟
                }
            }
        }
        }, `fetchParentPostConcurrent-${replyToPostId}`);
    }

    // 批量并行处理
    async function processBatchConcurrent(posts) {
        const batches = [];
        for (let i = 0; i < posts.length; i += CONFIG.PARALLEL_BATCH_SIZE) {
            batches.push(posts.slice(i, i + CONFIG.PARALLEL_BATCH_SIZE));
        }

        for (const batch of batches) {
            const promises = batch.map(post => {
                const replyTab = post.querySelector('.reply-to-tab');
                const replyToPostId = replyTab?.getAttribute('aria-controls');
                const cleanId = replyToPostId ? cleanPostId_func(replyToPostId) : '';

                if (cleanId && !post.hasAttribute('data-quote-added')) {
                    post.setAttribute('data-quote-added', 'true');
                    return fetchParentPostConcurrent(cleanId, post);
                }
                return Promise.resolve();
            });

            // 等待当前批次完成再处理下一批次
            await Promise.allSettled(promises);

            // 批次间的短暂延迟，避免过载
            if (batches.indexOf(batch) < batches.length - 1) {
                await new Promise(resolve => setTimeout(resolve, CONFIG.RETRY_DELAY));
            }
        }
    }



    // ========== 多线程状态监控 ==========
    function getMultiThreadStatus() {
        return {
            activeFetches,
            maxConcurrentFetches: CONFIG.MAX_CONCURRENT_FETCHES,
            pendingFetches: pendingFetches.length,
            activePromises: fetchPromises.size,
            activeTimeouts: fetchTimeouts.size,
            cacheSize: postCache.size
        };
    }

    // ========== 页面切换检测 ==========
    function handlePageChange() {
        log('检测到页面变化，立即重置并处理');

        // 确保DOM准备好
        if (!document.body) {
            log('DOM未准备好，延迟处理页面变化');
            setTimeout(handlePageChange, 50);
            return;
        }

        // 清除所有已处理标记
        const allTopicItems = document.querySelectorAll('.topic-list-item[data-metadata-processed]');
        allTopicItems.forEach(item => {
            item.removeAttribute('data-metadata-processed');
        });
        // 立即处理所有元素
        processAllExistingTopicItems();
    }

    // ========== 美化功能：添加楼主标识 ==========
    function addTopicOwnerClass() {
        // 检查当前是否在帖子详情页
        if (!window.location.pathname.startsWith('/t/')) {
            return;
        }

        log('添加楼主标识...');

        // 获取第一个帖子（通常是楼主帖）
        const firstPost = document.querySelector('#post_1');
        if (!firstPost) {
            log('未找到第一个帖子');
            return;
        }

        // 检查是否已有topic-owner类
        if (firstPost.classList.contains('topic-owner')) {
            log('第一个帖子已有topic-owner类');
            return;
        }

        // 获取第一个帖子的作者
        const firstPostAuthor = firstPost.querySelector('[data-user-card]');
        if (!firstPostAuthor) {
            log('未找到第一个帖子的作者信息');
            return;
        }

        const firstPostUsername = firstPostAuthor.getAttribute('data-user-card');

        // 获取主题作者（从主题元数据中获取）
        const topicAuthor = document.querySelector('.topic-meta-data [data-user-card]');
        if (!topicAuthor) {
            log('未找到主题作者信息');
            return;
        }

        const topicUsername = topicAuthor.getAttribute('data-user-card');

        // 比较作者是否一致，如果一致则添加topic-owner类
        if (firstPostUsername === topicUsername) {
            firstPost.classList.add('topic-owner');
            log(`为第一个帖子添加topic-owner类 (作者: ${firstPostUsername})`);
        } else {
            log(`第一个帖子作者 (${firstPostUsername}) 与主题作者 (${topicUsername}) 不一致`);
        }
    }

    // ========== 智能批量处理 ==========
    function processAllPosts() {
        const now = Date.now();
        if (isProcessing || (now - lastProcessTime < CONFIG.THROTTLE_DELAY)) return;

        isProcessing = true;
        lastProcessTime = now;

        try {
            // 检测是否为长帖子
            detectLongPost();

            const allPosts = Array.from(document.querySelectorAll('article[id^="post_"]:not([data-quote-added]):not([data-quote-deferred]):not([data-quote-processed="true"])'));
            if (!allPosts.length) {
                isProcessing = false;
                return;
            }

            // 在长帖子模式下，优先处理视口内的帖子
            let postsToProcess;
            if (isLongPost) {
                const viewportPosts = allPosts.filter(post => isElementNearViewport(post));
                const nearbyPosts = allPosts.filter(post => !isElementNearViewport(post, 0) && isElementNearViewport(post));
                postsToProcess = [...viewportPosts, ...nearbyPosts.slice(0, CONFIG.MAX_POSTS_PER_BATCH - viewportPosts.length)];
                log(`长帖子模式: 处理${viewportPosts.length}个视口内帖子，${nearbyPosts.length}个附近帖子`);
            } else {
                postsToProcess = allPosts.slice(0, CONFIG.MAX_POSTS_PER_BATCH);
            }

            log(`处理帖子批次: ${postsToProcess.length}/${allPosts.length}`);

            // 对于长帖子，使用并发批量处理
            if (isLongPost && postsToProcess.length > CONFIG.PARALLEL_BATCH_SIZE) {
                log('检测到长帖子，启用并发批量处理');
                processBatchConcurrent(postsToProcess).then(() => {
                    log('并发批量处理完成');
                }).catch(error => {
                    log('并发批量处理出错:', error);
                });
            } else {
                // 使用队列系统处理帖子
                postsToProcess.forEach((post, index) => {
                    const priority = isElementNearViewport(post, 0) ? 2 : (isElementNearViewport(post) ? 1 : 0);
                    setTimeout(() => {
                        addToProcessingQueue(post, priority);
                    }, index * (CONFIG.PROCESS_DELAY / CONFIG.BATCH_SIZE));
                });
            }

            isProcessing = false;

        } catch (error) {
            log('处理帖子批次出错:', error);
            isProcessing = false;
        }
    }

    // ========== 处理延迟的帖子 ==========
    function processDeferredPosts() {
        const deferredPosts = document.querySelectorAll('article[data-quote-deferred="true"]');
        deferredPosts.forEach(post => {
            if (isElementNearViewport(post)) {
                post.removeAttribute('data-quote-deferred');
                addToProcessingQueue(post, 1);
            }
        });
    }

    // ========== 节流函数 ==========
    function throttle(func, delay) {
        let lastCall = 0;
        let timeoutId = null;

        return function(...args) {
            const now = Date.now();
            const remaining = delay - (now - lastCall);

            if (remaining <= 0) {
                clearTimeout(timeoutId);
                timeoutId = null;
                lastCall = now;
                func.apply(this, args);
            } else if (!timeoutId) {
                timeoutId = setTimeout(() => {
                    lastCall = Date.now();
                    timeoutId = null;
                    func.apply(this, args);
                }, remaining);
            }
        };
    }



    // ========== DOM观察器 ==========
    function initObserver() {
        // 确保DOM准备好后再初始化观察器
        if (!document.body) {
            log('DOM未准备好，延迟初始化观察器');
            setTimeout(initObserver, 50);
            return;
        }

        // 创建专门的美化功能观察器 - 最高优先级
        const beautifyObserver = new MutationObserver(function(mutations) {
            // 立即处理美化功能，不使用任何延迟或节流
            for (const mutation of mutations) {
                if (mutation.type !== 'childList' || mutation.addedNodes.length === 0) continue;

                Array.from(mutation.addedNodes).forEach(node => {
                    if (node.nodeType !== Node.ELEMENT_NODE) return;

                    // 立即检查并处理topic-list-item
                    if (node.matches?.('.topic-list-item')) {
                        log('立即处理新的topic-list-item');
                        processTopicItemImmediately(node);
                    } else if (node.querySelector?.('.topic-list-item')) {
                        log('立即处理容器中的topic-list-item');
                        const topicItems = node.querySelectorAll('.topic-list-item');
                        topicItems.forEach(item => processTopicItemImmediately(item));
                    }
                });
            }
        });

        // 回复重构的节流处理 - 较低优先级
        const throttledReplyHandler = throttle((mutations) => {
            const hasNewPosts = mutations.some(mutation =>
                mutation.type === 'childList' &&
                Array.from(mutation.addedNodes).some(node =>
                    node.nodeType === Node.ELEMENT_NODE &&
                    (node.matches?.('article[id^="post_"]') ||
                     node.querySelector?.('article[id^="post_"]'))
                )
            );

            if (hasNewPosts) {
                log('检测到新帖子');
                updateFloorNumbers();
                processAllPosts();
            }
        }, CONFIG.THROTTLE_DELAY);

        // 创建回复重构观察器
        const replyObserver = new MutationObserver(throttledReplyHandler);

        const mainContent = document.querySelector('#main-outlet') || document.body;

        if (mainContent) {
            log('成功初始化DOM观察器');
            // 美化观察器 - 最高优先级，立即响应
            beautifyObserver.observe(mainContent, {
                childList: true,
                subtree: true,
                attributes: false,
                characterData: false
            });

            // 回复重构观察器 - 较低优先级
            replyObserver.observe(mainContent, {
                childList: true,
                subtree: true,
                attributes: false,
                characterData: false
            });
        } else {
            log('未找到主要内容容器，延迟重试');
            setTimeout(initObserver, 100);
        }
    }

    // ========== 立即处理单个topic-list-item ==========
    function processTopicItemImmediately(item) {
        // 如果已经处理过，跳过
        if (item.dataset.metadataProcessed === 'true' || item.querySelector('.topic-metadata-container')) {
            return;
        }

        // 立即标记为正在处理
        item.dataset.metadataProcessed = 'processing';

        const mainLink = item.querySelector('td.main-link');
        if (!mainLink) {
            item.dataset.metadataProcessed = 'true';
            return;
        }

        // 立即创建元数据容器
        const metadataContainer = document.createElement('div');
        metadataContainer.className = 'topic-metadata-container';
        metadataContainer.style.opacity = '1'; // 直接显示，不要渐入效果

        let hasMetadata = false;

        // 立即获取所有信息
        const userLink = item.querySelector('a[data-user-card]');
        const viewsElement = item.querySelector('td.num.views');
        const heatmapElement = item.querySelector('.topic-list-data.heatmap-low');
        const postsElement = item.querySelector('td.num.posts, td.num.posts-map.posts');
        const ageElement = item.querySelector('td.activity.num.topic-list-data.age');
        const activityElement = item.querySelector('td.num.activity');

        // 批量创建所有元数据元素
        if (userLink) {
            const authorName = userLink.getAttribute('data-user-card');
            if (authorName) {
                const authorElement = document.createElement('div');
                authorElement.className = 'topic-author-name topic-metadata-item';
                authorElement.textContent = authorName;
                metadataContainer.appendChild(authorElement);
                hasMetadata = true;
            }
        }

        if (viewsElement && viewsElement.textContent.trim()) {
            const viewsMetadata = document.createElement('div');
            viewsMetadata.className = 'topic-metadata-item views-metadata';
            const viewsIcon = document.createElement('span');
            viewsIcon.className = 'views-icon';
            viewsMetadata.appendChild(viewsIcon);
            viewsMetadata.appendChild(document.createTextNode(viewsElement.textContent));
            metadataContainer.appendChild(viewsMetadata);
            hasMetadata = true;
        }

        if (heatmapElement && heatmapElement.textContent.trim()) {
            const heatmapMetadata = document.createElement('div');
            heatmapMetadata.className = 'topic-metadata-item heatmap-metadata';
            const heatmapIcon = document.createElement('span');
            heatmapIcon.className = 'heatmap-icon';
            heatmapMetadata.appendChild(heatmapIcon);
            heatmapMetadata.appendChild(document.createTextNode(heatmapElement.textContent));
            metadataContainer.appendChild(heatmapMetadata);
            hasMetadata = true;
        }

        if (postsElement && postsElement.textContent.trim()) {
            const postsMetadata = document.createElement('div');
            postsMetadata.className = 'topic-metadata-item posts-metadata';
            const postsIcon = document.createElement('span');
            postsIcon.className = 'posts-icon';
            postsMetadata.appendChild(postsIcon);
            postsMetadata.appendChild(document.createTextNode(postsElement.textContent));
            metadataContainer.appendChild(postsMetadata);
            hasMetadata = true;
        }

        if (ageElement && ageElement.textContent.trim()) {
            const timeMetadata = document.createElement('div');
            timeMetadata.className = 'topic-metadata-item age-metadata';
            const ageIcon = document.createElement('span');
            ageIcon.className = 'age-icon';
            timeMetadata.appendChild(ageIcon);
            timeMetadata.appendChild(document.createTextNode(ageElement.textContent));
            metadataContainer.appendChild(timeMetadata);
            hasMetadata = true;
        } else if (activityElement && activityElement.textContent.trim()) {
            const timeMetadata = document.createElement('div');
            timeMetadata.className = 'topic-metadata-item activity-metadata';
            const activityIcon = document.createElement('span');
            activityIcon.className = 'activity-icon';
            timeMetadata.appendChild(activityIcon);
            timeMetadata.appendChild(document.createTextNode(activityElement.textContent));
            metadataContainer.appendChild(timeMetadata);
            hasMetadata = true;
        }

        // 立即插入DOM
        if (hasMetadata) {
            mainLink.appendChild(metadataContainer);
            item.dataset.metadataProcessed = 'true';
            log('元数据容器立即创建完成');
        } else {
            item.dataset.metadataProcessed = 'true';
        }
    }

    // ========== 安全的DOM操作函数 ==========
    function safeAddClassToBody() {
        if (document.body) {
            document.body.classList.add('linux-do-beautify');
            return true;
        }
        return false;
    }

    function waitForBody(callback) {
        if (document.body) {
            callback();
        } else {
            // 如果body还没准备好，等待一下
            setTimeout(() => waitForBody(callback), 10);
        }
    }

    // ========== 立即处理所有现有元素 ==========
    function processAllExistingTopicItems() {
        // 确保DOM已准备好
        if (!document.body) {
            log('DOM未准备好，延迟处理');
            setTimeout(processAllExistingTopicItems, 50);
            return;
        }

        const allTopicItems = document.querySelectorAll('.topic-list-item');
        log(`立即处理${allTopicItems.length}个现有的topic-list-item`);

        allTopicItems.forEach(item => {
            processTopicItemImmediately(item);
        });
    }

    // ========== 初始化 ==========
    function init() {
        log(`${isFirefox ? 'Firefox' : 'Chrome'}合并优化版开始初始化`);

        // 最高优先级：初始化网站原生加载检测
        detectNativeLoading();

        // 立即初始化样式
        initStyles();
        initBeautifyStyles();

        // 初始化屏蔽功能
        if (typeof GM_getValue !== 'undefined' && typeof GM_setValue !== 'undefined') {
            // 确保jQuery加载后再初始化屏蔽功能
            if (document.readyState === "loading") {
                document.addEventListener("DOMContentLoaded", () => checkJQuery(initBlockingFeature));
            } else {
                checkJQuery(initBlockingFeature);
            }
        }

        // 安全地添加美化类到body
        waitForBody(() => {
            safeAddClassToBody();
            log('成功添加美化类到body');
        });

        // 初始化页面路径跟踪
        window.lastPath = window.location.pathname;

        // 等待DOM准备好后处理现有元素
        waitForBody(() => {
            processAllExistingTopicItems();
        });

        window.isScrolling = false;

        // 等待DOM准备好后初始化观察器
        waitForBody(() => {
            initObserver();
        });

        // 简化的初始化处理
        const initProcess = () => {
            updateFloorNumbers();
            waitForBody(() => {
                processAllExistingTopicItems(); // 再次确保处理
            });
            addTopicOwnerClass();
            setTimeout(() => {
                processAllPosts();
            }, CONFIG.PROCESS_DELAY);
        };

        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', initProcess);
        } else {
            initProcess();
        }

        window.addEventListener('load', () => {
            updateFloorNumbers();
            waitForBody(() => {
                processAllExistingTopicItems(); // 确保load后也处理
            });
            addTopicOwnerClass();

            // 使用超级预加载系统
            setTimeout(() => {
                superPreloadQuotes();
            }, 50); // 减少延迟，更快启动

            processAllPosts();
        });

        // 页面切换时的处理（监听历史记录变化）
        window.addEventListener('popstate', () => {
            log('检测到历史记录变化');
            setTimeout(() => {
                handlePageChange();
            }, 50); // 减少延迟
        });

        // 初始化网络监听
        initNetworkMonitoring();

        // 优化头像加载，避免重复请求
        const avatarCache = new Set();
        // 安全代理 Image，不篡改原型和只读属性
        const OriginalImage = window.Image;
        window.Image = function(width, height) {
            const img = new OriginalImage(width, height);
            const setSrc = (value) => {
                if (typeof value === 'string' && value.includes('/user_avatar/')) {
                    if (avatarCache.has(value)) return;
                    avatarCache.add(value);
                }
                return value;
            };
            const descriptor = Object.getOwnPropertyDescriptor(Object.getPrototypeOf(img), 'src');
            if (descriptor && descriptor.set && descriptor.get) {
                const originalSetter = descriptor.set.bind(img);
                Object.defineProperty(img, 'src', {
                    configurable: true,
                    enumerable: descriptor.enumerable,
                    get: descriptor.get.bind(img),
                    set: (value) => originalSetter(setSrc(value))
                });
            }
            return img;
        };
        window.Image.prototype = OriginalImage.prototype;

        // 优化的路径变化监听 - 减少与论坛冲突
        let lastUrl = location.href;
        let urlChangeTimer = null;
        new MutationObserver(() => {
            // 减少处理频率，避免与论坛的DOM操作冲突
            if (isNativeLoading || detectNativeLoading()) {
                return;
            }

            const url = location.href;
            if (url !== lastUrl) {
                lastUrl = url;

                // 清除之前的定时器
                if (urlChangeTimer) {
                    clearTimeout(urlChangeTimer);
                }

                // 延迟处理，确保论坛导航完成
                urlChangeTimer = setTimeout(() => {
                    if (!isNativeLoading && !detectNativeLoading()) {
                        handlePageChange();
                    }
                }, 300); // 增加延迟
            }
        }).observe(document, {
            subtree: true,
            childList: true,
            attributes: false, // 减少监听范围
            characterData: false // 减少监听范围
        });

        // 简化的DOMContentLoaded处理
        document.addEventListener('DOMContentLoaded', function() {
            log('DOMContentLoaded - 立即处理');
            safeAddClassToBody();
            processAllExistingTopicItems();
            addTopicOwnerClass();
        });

        // 恢复核心功能的滚动事件处理 - 平衡功能与冲突避免
        const scrollThrottle = isFirefox ? 300 : 250; // 恢复合理的节流延迟
        const scrollEndDelay = isFirefox ? 600 : 500; // 恢复合理的延迟
        let lastScrollTime = 0;
        let scrollProcessingLock = false;


        window.addEventListener('scroll', throttle(() => {
            const now = Date.now();

            // 基本的原生加载检测
            if (isNativeLoading) {
                return;
            }

            // 防止重复处理
            if (scrollProcessingLock) {
                return;
            }

            // 在长帖子模式下，适度减少处理频率
            if (isLongPost && now - lastScrollTime < 1000) {
                return;
            }

            lastScrollTime = now;
            window.isScrolling = true;
            scrollProcessingLock = true;

            // 延迟处理，让网站原生功能优先
            setTimeout(() => {
                try {
                    // 再次检查是否有原生加载
                    if (isNativeLoading) {
                        return;
                    }

                    // 只在非长帖子模式下更新楼层号
                    if (!isLongPost) {
                        updateFloorNumbers();
                    }

                    // 智能预加载 - 在滚动时预加载视口附近的引用
                    if (CONFIG.SMART_PRELOAD_ENABLED && Math.random() < 0.3) {
                        smartViewportPreload();
                    }

                    // 在长帖子模式下，适度频率处理延迟帖子
                    if (isLongPost && Math.random() < 0.2) {
                        processDeferredPosts();
                    }
                } finally {
                    scrollProcessingLock = false;
                }
            }, 50); // 减少延迟

            clearTimeout(window.scrollEndTimer);
            window.scrollEndTimer = setTimeout(() => {
                window.isScrolling = false;

                // 如果检测到网站原生加载，跳过处理
                if (isNativeLoading) {
                    return;
                }

                // 优化处理频率
                if (!isLongPost) {
                    // 减少延迟，提高响应速度
                    setTimeout(() => {
                        if (!isNativeLoading) {
                            processAllPosts();
                            processDeferredPosts();
                            waitForBody(() => {
                                processAllExistingTopicItems();
                            });
                        }
                    }, 100);
                } else if (Math.random() < 0.1) {
                    // 长帖子模式下10%的概率处理
                    setTimeout(() => {
                        if (!isNativeLoading) {
                            processAllPosts();
                            processDeferredPosts();
                        }
                    }, 200);
                }

                // 滚动结束后进行智能预加载
                if (CONFIG.SMART_PRELOAD_ENABLED) {
                    setTimeout(() => {
                        if (!isNativeLoading) {
                            smartViewportPreload();
                            processDeferredPosts();
                        }
                    }, 300);
                }
            }, scrollEndDelay);
        }, scrollThrottle), { passive: true });

        // 定期维护
        setInterval(() => {
            // 清理缓存（包括过期和错误的缓存）
            cleanupCache();

            // 清理处理队列中的过期项目
            processingQueue = processingQueue.filter(item =>
                item.post?.isConnected && (now - item.timestamp < 30000)
            );

            // 清理超时的请求
            for (const [replyToPostId, timeoutId] of fetchTimeouts.entries()) {
                if (timeoutId && (now - timeoutId > CONFIG.FETCH_TIMEOUT * 2)) {
                    clearTimeout(timeoutId);
                    fetchTimeouts.delete(replyToPostId);
                    fetchPromises.delete(replyToPostId);
                    log(`清理超时请求: ${replyToPostId}`);
                }
            }

            // 清理等待队列中的过期项目
            const validPendingFetches = pendingFetches.filter(item =>
                item.post?.isConnected && (now - (item.timestamp || 0) < 30000)
            );
            if (validPendingFetches.length !== pendingFetches.length) {
                pendingFetches.length = 0;
                pendingFetches.push(...validPendingFetches);
                log(`清理等待队列: ${pendingFetches.length}个有效请求`);
            }

            // 保护楼层号
            updateFloorNumbers();

            // 定期检查未处理的元素（在长帖子模式下降低频率）
            if (document.body && (!isLongPost || Math.random() < 0.2)) {
                processAllExistingTopicItems();
            }

            // 在长帖子模式下，定期处理延迟的帖子
            if (isLongPost) {
                processDeferredPosts();
            }
        }, CONFIG.CACHE_CLEAN_INTERVAL);

        window.addEventListener('beforeunload', () => {
            if (rafId) cancelAnimationFrame(rafId);
        });

        log(`${isFirefox ? 'Firefox' : 'Chrome'}合并优化版初始化完成`);
    }

    // ========== 智能全局错误处理 ==========
    window.addEventListener('error', function(event) {
        if (!event.error) return;

        const errorMsg = event.error.message || '';
        const errorStack = event.error.stack || '';

        // 检查是否是我们脚本相关的错误
        const isOurError = errorStack.includes('linux.do') ||
                          errorStack.includes('optimized-reply-quote') ||
                          errorStack.includes('userscript') ||
                          errorStack.includes('tampermonkey');

        // 检查是否是变量未定义错误（如 't is undefined'）
        if (errorMsg.includes('is undefined') || errorMsg.includes('is not defined')) {
            // 如果是我们脚本相关的，进行处理
            if (isOurError) {
                log('检测到脚本相关的变量未定义错误:', errorMsg);
                event.preventDefault();
                event.stopPropagation();
                return;
            }
            // 如果不是我们的错误，完全不干预，让网站自行处理
            return;
        }

        // 检查是否是网站原生代码的错误
        if (!isOurError) {
            // 如果是网站原生错误，暂停我们的处理以避免冲突
            if (errorMsg.includes('TypeError') || errorMsg.includes('ReferenceError')) {
                // 记录但不改变加载状态，也不拦截
                log('检测到网站原生错误（仅记录，不干预）:', errorMsg);
            }
            return; // 不干预网站原生错误
        }

        // 只处理我们脚本相关的DOM错误
        if (isOurError && errorMsg) {
            log('检测到脚本相关错误:', errorMsg);

            const errorMsgLower = errorMsg.toLowerCase();
            if (errorMsgLower.includes('insertbefore') ||
                errorMsgLower.includes('argument 1 is not an object') ||
                errorMsgLower.includes('dom') ||
                errorMsgLower.includes('node')) {
                log('检测到DOM操作错误，尝试恢复:', errorMsg);

                // 清理可能有问题的DOM操作队列
                domOperationQueue.length = 0;
                if (domBatchTimer) {
                    clearTimeout(domBatchTimer);
                    domBatchTimer = null;
                }

                // 重置模板
                quoteTemplate = null;

                // 不再修改 isNativeLoading，也不阻止事件传播
            }
        }

        // 尝试恢复基本功能（只对我们的错误）
        if (isOurError && document.body && !document.body.classList.contains('linux-do-beautify')) {
            safeAddClassToBody();
        }
    });

    // 添加未捕获的Promise错误处理
    window.addEventListener('unhandledrejection', function(event) {
        log('未捕获的Promise错误（仅记录，不干预）:', event.reason);
    });

    // ========== 启动 ==========
    try {
        init();
    } catch (error) {
        log('初始化错误:', error);
        // 尝试基本的样式初始化
        setTimeout(() => {
            try {
                initStyles();
                initBeautifyStyles();
                if (document.body) {
                    safeAddClassToBody();
                }
            } catch (e) {
                log('恢复初始化失败:', e);
            }
        }, 1000);
    }






    // ========== 回复引用系统 ==========

    // 模板缓存系统
    let quoteTemplate = null;

    // 初始化模板
    function initQuoteTemplate() {
        if (quoteTemplate && quoteTemplate.content) return quoteTemplate;

        try {
            // 检查浏览器是否支持template元素
            if (typeof HTMLTemplateElement === 'undefined') {
                log('浏览器不支持template元素，使用降级方案');
                throw new Error('不支持template元素');
            }

            quoteTemplate = document.createElement('template');
            if (!quoteTemplate) {
                throw new Error('无法创建template元素');
            }

            // 使用安全的DOM操作创建模板内容
            const blockquote = document.createElement('blockquote');
            blockquote.className = 'optimized-reply-quote';

            const header = document.createElement('div');
            header.className = 'quote-header';

            const avatar = document.createElement('img');
            avatar.className = 'quote-avatar';
            avatar.style.display = 'none';
            avatar.alt = '';

            const author = document.createElement('span');
            author.className = 'quote-author';

            const content = document.createElement('div');
            content.className = 'quote-content';

            header.appendChild(avatar);
            header.appendChild(author);
            blockquote.appendChild(header);
            blockquote.appendChild(content);

            quoteTemplate.content.appendChild(blockquote);

            // 验证模板内容
            if (!quoteTemplate.content || !quoteTemplate.content.querySelector('.optimized-reply-quote')) {
                throw new Error('模板内容无效');
            }

            return quoteTemplate;
        } catch (error) {
            log('初始化模板失败，使用简化方案:', error);
            // 不使用template，直接返回null让createElement使用简化创建方式
            quoteTemplate = null;
            return null;
        }
    }

    // 批量DOM操作队列
    let domOperationQueue = [];
    let domBatchTimer = null;

    // 智能内容处理函数
    function processQuoteContent(rawContent) {
        if (!rawContent) return '';

        try {
            // 如果rawContent是DOM元素，直接处理
            if (rawContent instanceof Element) {
                const contentClone = rawContent.cloneNode(true);

                // 移除可能导致问题的元素
                const problematicElements = contentClone.querySelectorAll('script, style, iframe, object, embed');
                problematicElements.forEach(el => el.remove());

                // 获取纯文本长度进行检查
                const textContent = contentClone.textContent || contentClone.innerText || '';
                const textLength = textContent.length;

                // 根据内容长度采用不同策略
                if (textLength <= 500) {
                    // 短内容：保持原样
                    return contentClone;
                } else if (textLength <= CONFIG.MAX_QUOTE_LENGTH) {
                    // 中等长度：保持原样但可能需要优化
                    return contentClone;
                } else {
                    // 长内容：智能截断
                    log(`检测到长内容 (${textLength}字符)，进行智能截断`);

                    // 创建截断后的容器
                    const truncatedDiv = document.createElement('div');
                    const maxLength = CONFIG.MAX_QUOTE_LENGTH;
                    let currentLength = 0;

                    // 优先按段落截断
                    const paragraphs = contentClone.querySelectorAll('p, div');
                    if (paragraphs.length > 0) {
                        for (const p of paragraphs) {
                            const pText = p.textContent || '';
                            if (currentLength + pText.length > maxLength) {
                                break;
                            }
                            truncatedDiv.appendChild(p.cloneNode(true));
                            currentLength += pText.length;
                        }
                    } else {
                        // 按字符截断
                        const fullText = textContent;
                        let cutPoint = maxLength;

                        // 寻找最近的句号
                        for (let i = maxLength; i > maxLength * 0.8; i--) {
                            if (fullText[i] === '。' || fullText[i] === '.' || fullText[i] === '！' || fullText[i] === '!') {
                                cutPoint = i + 1;
                                break;
                            }
                        }

                        truncatedDiv.textContent = fullText.substring(0, cutPoint);
                    }

                    if (currentLength >= maxLength) {
                        const ellipsis = document.createElement('span');
                        ellipsis.style.cssText = 'color: #999; font-style: italic;';
                        ellipsis.textContent = '... (内容过长已截断)';
                        truncatedDiv.appendChild(ellipsis);
                    }

                    return truncatedDiv;
                }
            }

            // 如果是字符串，转换为纯文本
            if (typeof rawContent === 'string') {
                const textOnly = rawContent.replace(/<[^>]*>/g, '');
                const div = document.createElement('div');
                div.textContent = textOnly.length > CONFIG.MAX_QUOTE_LENGTH ?
                    textOnly.substring(0, CONFIG.MAX_QUOTE_LENGTH) + '...' :
                    textOnly;
                return div;
            }

            return null;
        } catch (error) {
            log('处理引用内容时出错:', error);
            // 降级到纯文本处理
            const div = document.createElement('div');
            if (typeof rawContent === 'string') {
                const textOnly = rawContent.replace(/<[^>]*>/g, '');
                div.textContent = textOnly.length > CONFIG.MAX_QUOTE_LENGTH ?
                    textOnly.substring(0, CONFIG.MAX_QUOTE_LENGTH) + '...' :
                    textOnly;
            } else {
                div.textContent = '内容处理失败';
            }
            return div;
        }
    }

    // 虚拟引用对象
    class VirtualQuote {
        constructor(post, content, author, floorNumber, avatarSrc) {
            this.post = post;
            this.content = processQuoteContent(content); // 使用智能内容处理

            // 对作者名进行额外的清理，确保移除所有问题前缀
            this.author = this.cleanAuthorName(author);
            this.floorNumber = floorNumber;
            this.avatarSrc = avatarSrc;
            this.element = null;
        }

        // 清理作者名的专用函数
        cleanAuthorName(author) {
            if (!author || typeof author !== 'string') {
                return '未知用户';
            }

            // 强化的清理逻辑
            let cleanAuthor = author
                .replace(/#embedded-posts__\d+\s*/g, '') // 移除 #embedded-posts__数字
                .replace(/embedded-posts__\d+\s*/g, '') // 移除 embedded-posts__数字（无#）
                .replace(/#embedded-posts[^\s]*\s*/g, '') // 移除其他 embedded-posts 相关内容
                .replace(/embedded-posts[^\s]*\s*/g, '') // 移除其他 embedded-posts 相关内容（无#）
                .replace(/^#\d+\s*/, '') // 移除开头的 #数字
                .replace(/^\s*#\s*/, '') // 移除开头的单独 #
                .trim();

            // 如果仍然包含问题字符，进行更深度清理
            if (cleanAuthor.includes('embedded-posts') || cleanAuthor.includes('#')) {
                // 更激进的清理
                cleanAuthor = cleanAuthor
                    .replace(/.*embedded-posts.*?\s+/g, '') // 移除包含embedded-posts的整个部分
                    .replace(/.*#.*?\s+/g, '') // 移除包含#的整个部分
                    .replace(/[#]/g, '') // 移除所有#字符
                    .trim();
            }

            // 最终验证
            if (!cleanAuthor || cleanAuthor.length === 0 || cleanAuthor.length >= 50) {
                return '未知用户';
            }

            return cleanAuthor;
        }

        createElement() {
            if (this.element) return this.element;

            // 优先使用简化的直接创建方式，避免template和cloneNode的问题
            try {
                const quoteElement = document.createElement('blockquote');
                quoteElement.className = 'optimized-reply-quote';

                // 创建头部
                const header = document.createElement('div');
                header.className = 'quote-header';

                // 创建头像（如果有）
                if (this.avatarSrc) {
                    try {
                        const avatar = document.createElement('img');
                        avatar.className = 'quote-avatar';
                        avatar.src = this.avatarSrc;
                        avatar.alt = this.author || '';
                        avatar.style.cssText = 'width: 24px; height: 24px; border-radius: 4px; margin-right: 8px; border: 2px solid rgba(0, 0, 0); flex-shrink: 0;';
                        header.appendChild(avatar);
                    } catch (avatarError) {
                        log('创建头像失败:', avatarError);
                    }
                }

                // 创建作者名
                const authorSpan = document.createElement('span');
                authorSpan.className = 'quote-author';
                // 再次确保作者名是清理过的
                const displayText = this.cleanAuthorName(this.author);
                authorSpan.textContent = displayText;
                header.appendChild(authorSpan);

                // 创建内容
                const contentDiv = document.createElement('div');
                contentDiv.className = 'quote-content';

                // 安全设置内容
                try {
                    if (this.content instanceof Element) {
                        // 如果内容是DOM元素，直接添加
                        contentDiv.appendChild(this.content.cloneNode(true));
                    } else if (this.content && typeof this.content === 'string') {
                        // 如果是字符串，转换为纯文本
                        const textContent = this.content.replace(/<[^>]*>/g, '');
                        contentDiv.textContent = textContent;
                    } else {
                        contentDiv.textContent = '内容无效';
                    }
                } catch (error) {
                    log('设置引用内容失败，使用纯文本:', error);
                    try {
                        const textContent = (this.content || '').toString().replace(/<[^>]*>/g, '');
                        contentDiv.textContent = textContent || '内容无法显示';
                    } catch (textError) {
                        log('设置纯文本也失败:', textError);
                        contentDiv.textContent = '内容无法显示';
                    }
                }

                // 组装元素
                quoteElement.appendChild(header);
                quoteElement.appendChild(contentDiv);

                this.element = quoteElement;
                return quoteElement;
            } catch (error) {
                log('直接创建引用元素失败:', error);

                // 最简化的降级方案
                try {
                    const basicQuote = document.createElement('div');
                    basicQuote.className = 'optimized-reply-quote';

                    const safeAuthor = this.cleanAuthorName(this.author || '未知用户').replace(/[<>]/g, '');
                    const safeContent = (this.content || '').replace(/<[^>]*>/g, '').substring(0, 500);
                    const headerText = safeAuthor;

                    // 使用安全的DOM操作
                    const header = document.createElement('div');
                    header.className = 'quote-header';
                    header.textContent = headerText;

                    const content = document.createElement('div');
                    content.className = 'quote-content';
                    content.textContent = safeContent + (safeContent.length >= 500 ? '...' : '');

                    basicQuote.appendChild(header);
                    basicQuote.appendChild(content);
                    this.element = basicQuote;
                    return basicQuote;
                } catch (fallbackError) {
                    log('创建简化版本也失败:', fallbackError);
                    // 最后的文本降级方案
                    const textQuote = document.createElement('div');
                    textQuote.className = 'optimized-reply-quote';
                    textQuote.textContent = `引用 ${this.cleanAuthorName(this.author || '未知用户')}: [内容无法显示]`;
                    this.element = textQuote;
                    return textQuote;
                }
            }
        }

        insertIntoPost() {
            try {
                const regularContents = this.post.querySelector('.regular.contents');
                if (!regularContents) return false;

                // 仅使用 article 级别的标记，不在内部容器上标记
                const postContent = regularContents.querySelector('.cooked') || regularContents;
                if (!postContent || !postContent.isConnected) return false;

                const quoteElement = this.createElement();
                if (!quoteElement) return false;

                // 安全的DOM插入
                try {
                    // 使用DocumentFragment优化插入
                    const fragment = document.createDocumentFragment();
                    fragment.appendChild(quoteElement);

                    // 使用安全插入函数
                    if (!safeInsertBefore(postContent, fragment, postContent.firstChild)) {
                        log('安全插入失败，尝试直接appendChild');
                        postContent.appendChild(quoteElement);
                    }
                } catch (insertError) {
                    log('DOM插入失败，尝试直接appendChild:', insertError);
                    // 降级处理：直接appendChild
                    postContent.appendChild(quoteElement);
                }

                return true;
            } catch (error) {
                log('insertIntoPost出错:', error);
                return false;
            }
        }
    }

    // 批量DOM操作处理器
    function addToDOMQueue(virtualQuote) {
        domOperationQueue.push(virtualQuote);

        if (domBatchTimer) clearTimeout(domBatchTimer);

        domBatchTimer = setTimeout(() => {
            processDOMBatch();
        }, 5); // 5ms批处理，极快响应
    }

    function processDOMBatch() {
        if (domOperationQueue.length === 0) return;

        const batch = domOperationQueue.splice(0, 10); // 每批最多10个

        // 使用requestAnimationFrame确保在下一帧执行
        requestAnimationFrame(() => {
            let successCount = 0;
            let errorCount = 0;

            batch.forEach((virtualQuote, index) => {
                try {
                    if (!virtualQuote.post?.isConnected) {
                        log(`跳过已断开连接的帖子 ${index}`);
                        return;
                    }

                    // 检查内容长度，对超长内容进行特殊处理
                    let contentLength = 0;
                    if (virtualQuote.content instanceof Element) {
                        contentLength = (virtualQuote.content.textContent || '').length;
                    } else if (typeof virtualQuote.content === 'string') {
                        contentLength = virtualQuote.content.length;
                    }
                    if (contentLength > CONFIG.ULTRA_LONG_CONTENT_THRESHOLD) {
                        log(`检测到超长内容 (${contentLength}字符)，使用简化处理`);
                        // 对超长内容使用更安全的插入方式
                        insertSimplifiedQuote(virtualQuote);
                    } else {
                        // 正常处理
                        virtualQuote.insertIntoPost();
                    }

                    // 标记处理完成
                    virtualQuote.post.setAttribute('data-quote-processed', 'true');

                    // 隐藏reply-tab
                    const replyTab = virtualQuote.post.querySelector('.reply-to-tab');
                    if (replyTab) replyTab.style.display = 'none';

                    successCount++;

                } catch (error) {
                    errorCount++;
                    log(`批量DOM操作错误 (${index}/${batch.length}):`, error);

                    // 尝试降级处理
                    try {
                        insertFallbackQuote(virtualQuote);
                        virtualQuote.post?.setAttribute('data-quote-processed', 'true');
                        log(`降级处理成功: ${index}`);
                    } catch (fallbackError) {
                        log(`降级处理也失败: ${index}`, fallbackError);
                        // 最后的保险：至少隐藏reply-tab
                        try {
                            const replyTab = virtualQuote.post?.querySelector('.reply-to-tab');
                            if (replyTab) replyTab.style.display = 'none';
                        } catch (e) {
                            // 忽略最终错误
                        }
                    }
                }
            });

            if (successCount > 0 || errorCount > 0) {
                log(`批处理完成: 成功${successCount}个，失败${errorCount}个`);
            }

            // 如果还有待处理的，继续下一批
            if (domOperationQueue.length > 0) {
                setTimeout(processDOMBatch, errorCount > 0 ? 10 : 1); // 如果有错误，稍微延长间隔
            }
        });
    }

    // 简化的引用插入（用于超长内容）
    function insertSimplifiedQuote(virtualQuote) {
        try {
            const regularContents = virtualQuote.post.querySelector('.regular.contents');
            if (!regularContents) return false;

            // 仅使用 article 级别的标记，不在内部容器上标记
            const postContent = regularContents.querySelector('.cooked') || regularContents;
            if (!postContent || !postContent.isConnected) return false;

            // 创建简化的引用元素
            const simpleQuote = document.createElement('div');
            simpleQuote.className = 'optimized-reply-quote';
            simpleQuote.style.cssText = 'margin-bottom: 10px; padding: 8px 12px; background: rgba(0, 0, 0, 0.05); border-radius: 16px; font-size: 14px;';

            const header = document.createElement('div');
            header.className = 'quote-header';
            header.style.cssText = 'font-weight: bold; margin: 5px; color: #555;';
            // 确保作者名是清理过的
            const cleanAuthor = virtualQuote.cleanAuthorName ? virtualQuote.cleanAuthorName(virtualQuote.author) : virtualQuote.author;
            header.textContent = cleanAuthor;

            const content = document.createElement('div');
            content.className = 'quote-content';
            content.style.cssText = 'color: #666; line-height: 1.4; word-wrap: break-word; padding: 0 0 0 4px;';

            // 安全地设置内容（纯文本）
            let textContent = '';
            if (virtualQuote.content instanceof Element) {
                textContent = virtualQuote.content.textContent || '';
            } else if (typeof virtualQuote.content === 'string') {
                textContent = virtualQuote.content.replace(/<[^>]*>/g, '');
            }
            content.textContent = textContent.length > 800 ?
                textContent.substring(0, 800) + '... (长内容已简化显示)' :
                textContent;

            simpleQuote.appendChild(header);
            simpleQuote.appendChild(content);

            // 安全的DOM插入
            if (!safeInsertBefore(postContent, simpleQuote, postContent.firstChild)) {
                log('简化引用插入失败，尝试appendChild');
                try {
                    postContent.appendChild(simpleQuote);
                } catch (appendError) {
                    log('简化引用appendChild也失败:', appendError);
                    return false;
                }
            }

            return true;
        } catch (error) {
            log('insertSimplifiedQuote出错:', error);
            return false;
        }
    }

     // 超高速引用创建函数
     function createQuoteInstantly(post, { content, author, floorNumber, avatarSrc }) {
         const startTime = performance.now();
         try {
             // 创建虚拟引用对象
             const virtualQuote = new VirtualQuote(post, content, author, floorNumber, avatarSrc);

             // 添加到批量处理队列
             addToDOMQueue(virtualQuote);

             const endTime = performance.now();
             updatePerformanceStats(endTime - startTime);

             log(`引用创建排队: ${post.id} -> ${author} #${floorNumber} (${(endTime - startTime).toFixed(2)}ms)`);

         } catch (error) {
             log('创建引用时出错:', error);
         }
     }

     // 内存映射引用系统
     const postElementMap = new WeakMap();
     const quoteDataMap = new Map();

     // 建立帖子映射
      function buildPostMap() {
         const posts = document.querySelectorAll('article[id^="post_"]');

         posts.forEach(post => {
              const floorId = getFloorNumber(post);
              const cleanId = cleanPostId_func(floorId);
              if (cleanId) {
                  postElementMap.set(post, cleanId);

                 const content = post.querySelector('.cooked');
                 const author = post.querySelector('.names .username');
                 const avatar = post.querySelector('.topic-avatar img.avatar');

                 if (content && author) {
                      quoteDataMap.set(cleanId, {
                         content: content.cloneNode(true),
                         author: author.textContent.trim(),
                          floorNumber: cleanId,
                         avatarSrc: avatar?.getAttribute('src') || '',
                         element: post
                     });
                 }
             }
         });

         log(`建立帖子映射: ${quoteDataMap.size}个帖子`);
     }

     // 闪电般的引用解析
      function lightningQuoteExtraction(post) {
         const replyTab = post.querySelector('.reply-to-tab');
         const replyToPostId = replyTab?.getAttribute('aria-controls');

         if (!replyToPostId) return false;

         const targetId = cleanPostId_func(replyToPostId);
         const quoteData = quoteDataMap.get(targetId);

         if (quoteData) {
             // 立即创建引用，无需等待
             createQuoteInstantly(post, quoteData);
             return true;
         }

         return false;
     }

     // 超级预加载系统 - 优化版
      function superPreloadQuotes() {
         if (document.body?.classList.contains('super-preload-completed')) return;

         log('启动超级预加载系统...');
         const startTime = performance.now();

         // 建立帖子映射
         buildPostMap();

         // 批量处理所有引用
         const allPosts = document.querySelectorAll('article[id^="post_"]');
         let processedCount = 0;
         let networkRequestCount = 0;

         // 分批处理，避免阻塞
         const processBatch = (posts, batchIndex = 0) => {
             const batchSize = 10;
             const start = batchIndex * batchSize;
             const end = Math.min(start + batchSize, posts.length);
             const batch = Array.from(posts).slice(start, end);

             batch.forEach(post => {
                 if (post.hasAttribute('data-quote-added')) return;

                 // 优先使用闪电引用解析
                  if (lightningQuoteExtraction(post)) {
                     post.setAttribute('data-quote-added', 'true');
                     processedCount++;
                 } else if (CONFIG.SMART_PRELOAD_ENABLED) {
                     // 对于视口附近的帖子，启动网络请求
                     if (isElementNearViewport(post, CONFIG.VIEWPORT_PRELOAD_DISTANCE)) {
                         const replyTab = post.querySelector('.reply-to-tab');
                          const replyToPostId = replyTab?.getAttribute('aria-controls');
                          const cleanId = replyToPostId ? cleanPostId_func(replyToPostId) : '';

                          if (cleanId && networkRequestCount < 5) { // 限制预加载网络请求数量
                              post.setAttribute('data-quote-added', 'true');
                              fetchParentPostConcurrent(cleanId, post);
                             networkRequestCount++;
                         }
                     }
                 }
             });

             // 继续处理下一批
             if (end < posts.length) {
                 setTimeout(() => processBatch(posts, batchIndex + 1), 10);
             } else {
                 // 预加载完成
                 document.body?.classList.add('super-preload-completed');
                 const endTime = performance.now();
                 log(`超级预加载完成: ${processedCount}个即时引用，${networkRequestCount}个网络请求，耗时${(endTime - startTime).toFixed(2)}ms`);
             }
         };

         processBatch(allPosts);
     }

     // 恢复核心功能的智能视口预加载 - 平衡功能与冲突避免
     function smartViewportPreload() {
         if (!CONFIG.SMART_PRELOAD_ENABLED) return;

         // 基本检查，避免在明显冲突时预加载
         if (isNativeLoading) {
             return;
         }

          const posts = document.querySelectorAll('article[id^="post_"]:not([data-quote-added]):not([data-quote-processed="true"])');
         let preloadCount = 0;

         posts.forEach(post => {
             if (preloadCount >= 3) return; // 恢复合理的预加载数量

              if (isElementNearViewport(post, CONFIG.VIEWPORT_PRELOAD_DISTANCE)) {
                  const replyTab = post.querySelector('.reply-to-tab');
                  const replyToPostId = replyTab?.getAttribute('aria-controls');
                  const cleanId = replyToPostId ? cleanPostId_func(replyToPostId) : '';

                  if (cleanId) {
                      // 使用标准化的缓存键值检查
                      const cacheKey = generateCacheKey(cleanId);
                      const cachedPost = postCache.get(cacheKey);

                      if (!cachedPost && !fetchPromises.has(cleanId)) {
                          post.setAttribute('data-quote-added', 'true');
                          fetchParentPostConcurrent(cleanId, post);
                          preloadCount++;
                      }
                  }
              }
         });
     }

    // ========== 屏蔽功能模块 ==========
    // #region 屏蔽功能模块

    // 安全的存储操作函数
    function safeGetValue(key, defaultValue) {
        try {
            return GM_getValue(key, defaultValue) || defaultValue;
        } catch (error) {
            console.error(`Error getting value for ${key}:`, error);
            return defaultValue;
        }
    }

    function safeSetValue(key, value) {
        try {
            GM_setValue(key, value);
            return true;
        } catch (error) {
            console.error(`Error setting value for ${key}:`, error);
            showNotification(`保存失败: ${error.message}`);
            return false;
        }
    }

    // 获取存储的屏蔽列表
    let keywords = safeGetValue("filterKeywords", []);
    let blockedUsers = safeGetValue("filterUsers", []);
    let blockedCategories = safeGetValue("filterCategories", []);

    // 重新加载屏蔽列表
    function reloadBlockLists() {
        keywords = safeGetValue("filterKeywords", []);
        blockedUsers = safeGetValue("filterUsers", []);
        blockedCategories = safeGetValue("filterCategories", []);
    }

    // 显示通知函数
    function showNotification(message, duration = 3000) {
        let notification = document.querySelector(".notification");

        if (!notification) {
            notification = document.createElement("div");
            notification.className = "notification";
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: #333;
                color: white;
                padding: 10px 20px;
                border-radius: 5px;
                z-index: 10000;
                opacity: 0;
                transition: opacity 0.3s ease;
            `;
            document.body.appendChild(notification);
        }

        notification.textContent = message;
        notification.style.opacity = '1';

        setTimeout(() => {
            notification.style.opacity = '0';
        }, duration);
    }

    // 检查jQuery是否加载
    function checkJQuery(callback, maxAttempts = 10) {
        if (maxAttempts <= 0) {
            console.error("jQuery加载失败");
            return;
        }

        if (typeof jQuery !== "undefined") {
            callback();
        } else {
            setTimeout(() => checkJQuery(callback, maxAttempts - 1), 200);
        }
    }

    // 防抖函数
    function debounce(func, wait) {
        let timeout;
        return function (...args) {
            clearTimeout(timeout);
            timeout = setTimeout(() => func.apply(this, args), wait);
        };
    }

    // 创建触发区域和按钮
    function createBlockButton() {
        const triggerArea = document.createElement("div");
        triggerArea.className = "trigger-area";

        const button = document.createElement("button");
        button.innerText = "屏蔽列表";
        button.className = "block-list-button";
        button.addEventListener("click", showBlockListDialog);

        triggerArea.appendChild(button);
        document.body.appendChild(triggerArea);
    }

    // 获取帖子的用户信息
    function getPostUsers(post, excludeUsersList = false) {
        const users = [];

        // 简化：只检查 data-user-card 属性
        // 如果 excludeUsersList 为 true，则排除 topic-map__users-list 中的用户
        let userCardLinks;
        if (excludeUsersList) {
            // 排除 topic-map__users-list 中的用户
            userCardLinks = Array.from(post.querySelectorAll("[data-user-card]")).filter(link => {
                // 检查该元素是否在 topic-map__users-list 中
                return !link.closest('.topic-map__users-list');
            });
        } else {
            userCardLinks = post.querySelectorAll("[data-user-card]");
        }

        userCardLinks.forEach(link => {
            const username = link.getAttribute("data-user-card");
            if (username) {
                users.push(username);
            }
        });

        // 去重并过滤空值
        const uniqueUsers = [...new Set(users)].filter((user) => user);

        return uniqueUsers;
    }

    // 检查文本是否包含关键词（不区分大小写，严格匹配）
    function containsKeywords(text, keywordList) {
        if (!text || !keywordList || keywordList.length === 0) return false;

        // 检查当前是否在详情页，如果是则不进行关键词匹配
        const isDetailPage = window.location.pathname.startsWith('/t/');
        if (isDetailPage) {
            return false;
        }

        const lowerText = text.toLowerCase();

        return keywordList.some((keyword) => {
            // 转换关键词为小写
            const lowerKeyword = keyword.toLowerCase();

            // 检查关键词是否包含中文字符
            const containsChinese = /[\u4e00-\u9fa5]/.test(lowerKeyword);

            // 对中文和英文采用不同的匹配策略
            if (containsChinese) {
                // 对于中文：只要包含即可
                const chineseRegex = new RegExp(escapeRegExp(lowerKeyword), "i");
                return chineseRegex.test(lowerText);
            } else {
                // 对于英文：使用词边界匹配
                const regex = new RegExp(
                    "\\b" + escapeRegExp(lowerKeyword) + "\\b",
                    "i"
                );
                return regex.test(lowerText);
            }
        });
    }

    // 辅助函数：转义正则表达式中的特殊字符
    function escapeRegExp(string) {
        return string.replace(/[.*+?^${}()|[\]\\]/g, "\\$&"); // $&表示整个匹配的字符串
    }

    // 过滤帖子
    function filterPosts() {
        // 重新加载屏蔽列表，确保使用最新的数据
        reloadBlockLists();

        // 检查当前是否在主页
        const isHomePage = !window.location.pathname.startsWith('/t/');

        // 处理主页列表中的帖子 - 同时支持原始结构和美化后的结构
        const mainTopics = document.querySelectorAll(".topic-list-item");

        mainTopics.forEach((topic) => {
            // 获取标题文本，使用特定选择器只针对主页标题列表
            const titleElement = topic.querySelector(".main-link a.title");
            const title = titleElement ? titleElement.textContent.trim() : "";

            // 获取标签信息，检查是否包含敏感标签
            const tagElements = topic.querySelectorAll(".discourse-tag");
            const tags = Array.from(tagElements).map((tag) => tag.textContent.trim());
            const tagText = tags.join(" ");

            // 获取分类节点信息
            const categoryElement = topic.querySelector(".badge-category__name");
            const category = categoryElement ? categoryElement.textContent.trim() : "";

            // 获取作者信息 - 适应美化后的结构
            const users = getPostUsers(topic);

            // 检查标题是否包含关键词（不区分大小写）- 只在主页标题列表生效
            // 只有在主页时才检查关键词
            const hasKeyword = isHomePage && titleElement &&
                (containsKeywords(title, keywords) ||
                containsKeywords(tagText, keywords));

            // 检查是否包含屏蔽用户 - 确保检查所有可能的用户标识符
            // 只要有一个用户标识符在屏蔽列表中，就屏蔽该帖子
            // 使用精确匹配检查是否包含屏蔽用户
            // 逐个检查每个用户是否在屏蔽列表中
            let blockedUserFound = null;
            for (const user of users) {
                const isBlocked = blockedUsers.includes(user);
                if (isBlocked) {
                    blockedUserFound = user;
                    break;
                }
            }

            const hasBlockedUser = !!blockedUserFound;

            // 检查是否属于屏蔽的分类节点
            const hasBlockedCategory = category && blockedCategories.some(blockedCat => category.startsWith(blockedCat));

            if (hasKeyword || hasBlockedUser || hasBlockedCategory) {
                topic.classList.add("filtered-topic");
                // 确保样式被应用
                topic.style.display = "none !important";
                // 如果是美化后的结构，可能需要设置更高的优先级
                if (topic.style.display !== "none") {
                    topic.setAttribute("style", "display: none !important");
                }
            } else {
                topic.classList.remove("filtered-topic");
                // 移除可能添加的内联样式
                if (topic.style.display === "none") {
                    topic.style.removeProperty("display");
                }
            }
        });

        // 处理详情页中的帖子和回复
        const detailPosts = document.querySelectorAll(".topic-post");

        detailPosts.forEach((post) => {
            // 获取所有可能的用户标识符，排除用户列表中的用户
            const users = getPostUsers(post, true); // 传入 true 表示排除用户列表中的用户

            // 获取分类节点信息（在详情页可能需要从其他元素获取）
            const categoryElement = document.querySelector(".badge-category__name");
            const category = categoryElement ? categoryElement.textContent.trim() : "";

            // 检查是否包含屏蔽用户 - 修改为检查所有用户标识符
            // 只要有一个用户标识符在屏蔽列表中，就屏蔽该帖子
            // 使用精确匹配检查是否包含屏蔽用户
            // 逐个检查每个用户是否在屏蔽列表中
            let blockedUserFound = null;
            for (const user of users) {
                const isBlocked = blockedUsers.includes(user);
                if (isBlocked) {
                    blockedUserFound = user;
                    break;
                }
            }

            const hasBlockedUser = !!blockedUserFound;

            // 检查是否属于屏蔽的分类节点
            const hasBlockedCategory = category && blockedCategories.some(blockedCat => category.startsWith(blockedCat));

            // 注意：详情页中完全不检查关键词，关键词屏蔽只对主页标题列表生效
            // 确保在详情页中不会因为关键词而屏蔽帖子

            if (hasBlockedUser || hasBlockedCategory) {
                post.classList.add("filtered-topic");
                // 确保样式被应用
                post.style.display = "none !important";
                // 如果是美化后的结构，可能需要设置更高的优先级
                if (post.style.display !== "none") {
                    post.setAttribute("style", "display: none !important");
                }
            } else {
                post.classList.remove("filtered-topic");
                // 移除可能添加的内联样式
                if (post.style.display === "none") {
                    post.style.removeProperty("display");
                }
            }
        });
    }

    // 显示屏蔽列表对话框
    function showBlockListDialog() {
        // 检查是否已存在对话框
        if (document.querySelector(".block-dialog")) {
            return;
        }

        const dialog = document.createElement("div");
        dialog.className = "block-dialog";

        // 创建标题栏作为拖动区域
        const titleBar = document.createElement("div");
        titleBar.className = "block-title-bar";

        // 拖动实现
        let isDragging = false;
        let startX, startY;
        let initialLeft, initialTop;

        function startDragging(e) {
            if (e.target === titleBar) {
                isDragging = true;
                startX = e.clientX;
                startY = e.clientY;

                const rect = dialog.getBoundingClientRect();
                initialLeft = rect.left;
                initialTop = rect.top;

                dialog.style.transform = "none";
                dialog.style.left = `${initialLeft}px`;
                dialog.style.top = `${initialTop}px`;
            }
        }

        function doDrag(e) {
            if (!isDragging) return;

            e.preventDefault();
            const deltaX = e.clientX - startX;
            const deltaY = e.clientY - startY;

            dialog.style.left = `${initialLeft + deltaX}px`;
            dialog.style.top = `${initialTop + deltaY}px`;
        }

        function stopDragging() {
            isDragging = false;
        }

        titleBar.addEventListener("mousedown", startDragging);
        document.addEventListener("mousemove", doDrag);
        document.addEventListener("mouseup", stopDragging);

        // 创建关闭按钮
        const closeButton = document.createElement("button");
        closeButton.className = "block-close-button";
        closeButton.innerHTML =
            '<span class="block-close-line block-close-line-1"></span><span class="block-close-line block-close-line-2"></span>';
        closeButton.onclick = () => {
            document.removeEventListener("mousemove", doDrag);
            document.removeEventListener("mouseup", stopDragging);
            document.body.removeChild(dialog);
        };

        // 渲染用户列表为带样式的项目
        function renderUserList(users) {
            // 获取最近的30个用户名
            const recentUsers = users.slice(-30);
            if (recentUsers.length === 0) {
                return "<div style='font-style:italic;color:#999'>暂无屏蔽用户</div>";
            }

            return recentUsers
                .map((user) => `<span class="block-item">${user}</span>`)
                .join(" ");
        }

        // 渲染关键词列表为带样式的项目
        function renderKeywordList(keywords) {
            if (keywords.length === 0) {
                return "<div style='font-style:italic;color:#999'>暂无屏蔽关键词</div>";
            }

            return keywords
                .map((keyword) => `<span class="block-item">${keyword}</span>`)
                .join(" ");
        }

        // 渲染分类节点列表为带样式的项目
        function renderCategoryList(categories) {
            if (categories.length === 0) {
                return "<div style='font-style:italic;color:#999'>暂无屏蔽分类节点</div>";
            }

            return categories
                .map((category) => {
                    return `<span class="block-item">
                        ${category}
                    </span>`;
                })
                .join(" ");
        }

        // 显示当前屏蔽的用户名列表
        const userList = document.createElement("div");
        userList.className = "block-list";
        userList.innerHTML = `<h3 class="block-title">屏蔽的用户名 (显示最近30个):</h3>${renderUserList(
            blockedUsers
        )}`;

        // 创建用户名输入区域
        const userInputContainer = document.createElement("div");
        userInputContainer.className = "block-input-container";

        const userInput = document.createElement("input");
        userInput.type = "text";
        userInput.placeholder = "输入用户名";
        userInput.className = "block-input";

        const addUserButton = document.createElement("button");
        addUserButton.innerText = "添加用户名";
        addUserButton.className = "block-button";

        // 显示当前屏蔽的关键词列表
        const keywordList = document.createElement("div");
        keywordList.className = "block-list block-keyword-list";
        keywordList.innerHTML = `<h3 class="block-title">屏蔽的关键词:</h3>${renderKeywordList(
            keywords
        )}`;

        // 创建关键词输入区域
        const keywordInputContainer = document.createElement("div");
        keywordInputContainer.className = "block-input-container";

        const keywordInput = document.createElement("input");
        keywordInput.type = "text";
        keywordInput.placeholder = "输入关键词";
        keywordInput.className = "block-input";

        const addKeywordButton = document.createElement("button");
        addKeywordButton.innerText = "添加关键词";
        addKeywordButton.className = "block-button";

        // 添加用户名事件
        addUserButton.onclick = () => {
            const newUser = userInput.value.trim();
            if (newUser) {
                if (blockedUsers.includes(newUser)) {
                    showNotification(`用户 "${newUser}" 已在屏蔽列表中`);
                    return;
                }

                blockedUsers.push(newUser);
                if (safeSetValue("filterUsers", blockedUsers)) {
                    userList.innerHTML = `<h3 class="block-title">屏蔽的用户名 (显示最近30个):</h3>${renderUserList(
                        blockedUsers
                    )}`;
                    userInput.value = "";
                    filterPosts();
                    showNotification(`已添加屏蔽用户: ${newUser}`);
                }
            }
        };

        // 添加关键词事件
        addKeywordButton.onclick = () => {
            const newKeyword = keywordInput.value.trim();
            if (newKeyword) {
                // 检查是否已存在（不区分大小写）
                const exists = keywords.some(
                    (k) => k.toLowerCase() === newKeyword.toLowerCase()
                );
                if (exists) {
                    showNotification(`关键词 "${newKeyword}" 已在屏蔽列表中`);
                    return;
                }

                keywords.push(newKeyword);
                if (safeSetValue("filterKeywords", keywords)) {
                    keywordList.innerHTML = `<h3 class="block-title">屏蔽的关键词:</h3>${renderKeywordList(
                        keywords
                    )}`;
                    keywordInput.value = "";
                    filterPosts();
                    showNotification(`已添加屏蔽关键词: ${newKeyword}`);
                }
            }
        };

        // 添加回车键提交
        userInput.addEventListener("keypress", (e) => {
            if (e.key === "Enter") {
                addUserButton.click();
            }
        });

        keywordInput.addEventListener("keypress", (e) => {
            if (e.key === "Enter") {
                addKeywordButton.click();
            }
        });

        // 显示当前屏蔽的分类节点列表
        const categoryList = document.createElement("div");
        categoryList.className = "block-list block-category-list";
        categoryList.innerHTML = `<h3 class="block-title">屏蔽的分类节点:</h3>${renderCategoryList(
            blockedCategories
        )}`;

        // 创建分类节点输入区域
        const categoryInputContainer = document.createElement("div");
        categoryInputContainer.className = "block-input-container";

        const categoryInput = document.createElement("input");
        categoryInput.type = "text";
        categoryInput.placeholder = "输入分类节点名称";
        categoryInput.className = "block-input";

        const addCategoryButton = document.createElement("button");
        addCategoryButton.innerText = "添加分类节点";
        addCategoryButton.className = "block-button";

        // 添加分类节点事件
        addCategoryButton.onclick = () => {
            const newCategory = categoryInput.value.trim();
            if (newCategory) {
                // 检查是否已存在（不区分大小写）
                const exists = blockedCategories.some(
                    (c) => c.toLowerCase() === newCategory.toLowerCase()
                );
                if (exists) {
                    showNotification(`分类节点 "${newCategory}" 已在屏蔽列表中`);
                    return;
                }

                blockedCategories.push(newCategory);
                if (safeSetValue("filterCategories", blockedCategories)) {
                    categoryList.innerHTML = `<h3 class="block-title">屏蔽的分类节点:</h3>${renderCategoryList(
                        blockedCategories
                    )}`;
                    categoryInput.value = "";
                    filterPosts();
                    showNotification(`已添加屏蔽分类节点: ${newCategory}`);
                }
            }
        };

        // 添加回车键提交
        categoryInput.addEventListener("keypress", (e) => {
            if (e.key === "Enter") {
                addCategoryButton.click();
            }
        });

        // 组装界面元素
        userInputContainer.appendChild(userInput);
        userInputContainer.appendChild(addUserButton);
        keywordInputContainer.appendChild(keywordInput);
        keywordInputContainer.appendChild(addKeywordButton);

        // 组装分类节点输入区域
        categoryInputContainer.appendChild(categoryInput);
        categoryInputContainer.appendChild(addCategoryButton);

        dialog.appendChild(titleBar);
        dialog.appendChild(closeButton);
        dialog.appendChild(userList);
        dialog.appendChild(userInputContainer);
        dialog.appendChild(keywordList);
        dialog.appendChild(keywordInputContainer);
        dialog.appendChild(categoryList);
        dialog.appendChild(categoryInputContainer);

        document.body.appendChild(dialog);
    }

    // 在头像旁添加屏蔽按钮
    function addBlockUserButtonsToAvatars() {
        // 查找所有头像元素 - 帖子详情页、列表页和主页
        const avatarElements = document.querySelectorAll('.topic-avatar img, .avatar img, .topic-list-item .poster img, .topic-list .posters a img, .latest-topic-list .posters a img, td.posters a img');

        // 处理每个头像元素
        avatarElements.forEach(avatarImg => {
            // 跳过已处理的头像
            if (avatarImg.dataset.blockButtonAdded === 'true') return;

            // 获取用户名 - 处理不同的页面结构
            let username = '';

            // 1. 检查头像本身是否在链接内，链接上有data-user-card属性
            const parentLink = avatarImg.closest('a[data-user-card]');
            if (parentLink) {
                username = parentLink.getAttribute('data-user-card');
            }

            // 2. 如果头像上没有找到，则查找包含头像的帖子元素
            if (!username) {
                const postElement = avatarImg.closest('.topic-post') || avatarImg.closest('.topic-list-item');
                if (postElement) {
                    const userCardElement = postElement.querySelector('[data-user-card]');
                    if (userCardElement) {
                        username = userCardElement.getAttribute('data-user-card');
                    }
                }
            }

            // 3. 对于主页的特殊结构，可能需要查找父级元素中的用户名
            if (!username && avatarImg.closest('td.posters')) {
                const posterCell = avatarImg.closest('td.posters');
                const userLink = posterCell.querySelector('a[data-user-card]');
                if (userLink) {
                    username = userLink.getAttribute('data-user-card');
                }
            }

            // 如果所有方法都无法找到用户名，则跳过
            if (!username) return;

            // 标记头像已处理
            avatarImg.dataset.blockButtonAdded = 'true';

            // 检查用户是否已被屏蔽，如果已屏蔽则不添加按钮
            if (blockedUsers.includes(username)) return;

            // 检查是否已经添加了按钮容器
            let avatarContainer = avatarImg.closest('.avatar-container');
            if (!avatarContainer) {
                // 创建容器
                avatarContainer = document.createElement('div');
                avatarContainer.className = 'avatar-container';

                // 将头像包裹在容器中
                const parent = avatarImg.parentNode;
                parent.insertBefore(avatarContainer, avatarImg);
                avatarContainer.appendChild(avatarImg);
            }

            // 检查是否已经添加了屏蔽按钮
            if (avatarContainer.querySelector('.block-avatar-hover-button')) return;

            // 创建屏蔽按钮
            const blockButton = document.createElement('span');
            blockButton.textContent = '屏蔽用户';
            blockButton.className = 'block-avatar-hover-button';
            blockButton.title = `屏蔽用户: ${username}`;

            // 添加点击事件
            blockButton.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();

                // 检查用户是否已被屏蔽
                if (blockedUsers.includes(username)) {
                    showNotification(`用户 "${username}" 已在屏蔽列表中`);
                    return;
                }

                // 添加用户到屏蔽列表
                blockedUsers.push(username);
                if (safeSetValue("filterUsers", blockedUsers)) {
                    filterPosts(); // 重新过滤帖子
                    showNotification(`已添加屏蔽用户: ${username}`);

                    // 添加已屏蔽标记
                    const indicator = document.createElement('span');
                    indicator.textContent = ' (已屏蔽)';
                    indicator.className = 'blocked-user-indicator';
                    indicator.style.color = 'grey';
                    indicator.style.fontSize = '0.9em';
                    indicator.style.marginLeft = '4px';

                    // 查找用户名元素以添加标记
                    let userNameElement = null;

                    // 1. 首先尝试使用之前找到的parentLink
                    if (parentLink) {
                        userNameElement = parentLink;
                    }

                    // 2. 如果没有parentLink，尝试在帖子元素中查找
                    if (!userNameElement) {
                        const postElement = avatarImg.closest('.topic-post') || avatarImg.closest('.topic-list-item');
                        if (postElement) {
                            userNameElement = postElement.querySelector('[data-user-card]');
                        }
                    }

                    // 3. 对于主页的特殊结构
                    if (!userNameElement && avatarImg.closest('td.posters')) {
                        const posterCell = avatarImg.closest('td.posters');
                        userNameElement = posterCell.querySelector('a[data-user-card]');
                    }

                    // 将标记添加到用户名旁
                    if (userNameElement) {
                        if (userNameElement.nextSibling) {
                            userNameElement.parentNode.insertBefore(indicator, userNameElement.nextSibling);
                        } else {
                            userNameElement.parentNode.appendChild(indicator);
                        }
                    }

                    // 移除屏蔽按钮
                    blockButton.remove();

                    // 移除头像容器的特殊样式，恢复正常显示
                    if (avatarContainer) {
                        avatarContainer.style.marginBottom = '0';
                        // 创建一个新的普通容器替换avatar-container
                        const newContainer = document.createElement('div');
                        const parent = avatarContainer.parentNode;
                        while (avatarContainer.firstChild) {
                            newContainer.appendChild(avatarContainer.firstChild);
                        }
                        parent.replaceChild(newContainer, avatarContainer);
                    }
                }
            });

            // 将按钮添加到容器
            avatarContainer.appendChild(blockButton);
        });
    }

    // 添加事件监听器
    function addEventListeners() {
        // 添加事件监听器到屏蔽按钮
        const blockListButton = document.querySelector(".block-list-button");
        if (blockListButton) {
            blockListButton.addEventListener("click", showBlockListDialog);
        }
    }

    // 渲染UI
    function renderUI() {
        createBlockButton();
    }

    // 屏蔽功能初始化
    function initBlockingFeature() {
        // 仅在帖子详情页执行重构逻辑
        if (window.location.pathname.startsWith('/t/')) {
            renderUI();
            addEventListeners();

            // 立即执行一次过滤和头像处理
            // 增加延迟，确保美化脚本已经完成DOM修改
            setTimeout(() => {
                filterPosts();
                addBlockUserButtonsToAvatars(); // 添加头像旁的屏蔽按钮
            }, 1000);

            // 监听DOM变化，处理动态加载的内容
            const observer = new MutationObserver(debounce(() => {
                filterPosts();
                addBlockUserButtonsToAvatars(); // 添加头像旁的屏蔽按钮
            }, 500));

            // 配置观察器：观察目标节点（帖子容器）的子节点变化
             const targetNode = document.body.querySelector('.topic-posts') || document.body; // 优先观察 .topic-posts
             observer.observe(targetNode, { childList: true, subtree: true });

        } else {
             // 对于非帖子页面，可能只需要按钮和列表页过滤
             renderUI(); // 仍然显示按钮
             addEventListeners();

             // 增加延迟，确保美化脚本已经完成DOM修改
             setTimeout(() => {
                 filterPosts(); // 列表页过滤
                 addBlockUserButtonsToAvatars(); // 添加头像旁的屏蔽按钮
             }, 1000);

             // 列表页也可能需要监听器，以处理动态加载
             const listObserver = new MutationObserver(debounce(() => {
                filterPosts();
                addBlockUserButtonsToAvatars(); // 添加头像旁的屏蔽按钮
             }, 300));

             listObserver.observe(document.body, { childList: true, subtree: true });
        }

        // 额外的安全措施：定期检查并重新应用过滤
        setInterval(() => {
            filterPosts();
        }, 5000);
    }

    // #endregion

 })();